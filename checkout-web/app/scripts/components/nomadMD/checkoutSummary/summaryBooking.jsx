import React, { useState } from 'react';

import DateTime from '../../../dateTime';
import Divider from '../divider/divider';
import bemify from '../../../utils/bemUtils';
import { listFormat } from '../../../utils/stringUtils';
import { useQuery } from '@tanstack/react-query';
import { parseSelectedProcedures } from '../../../utils/nomadMD';
import { isLoadingQuery } from '../../../utils/networkUtils';
import ImageAsync from '../imageAsync/imageAsync';
import Accordion from '../../accordion/accordion';
import AccordionPanel from '../../accordion/accordionPanel';
import { formatCurrency } from '../../../utils/currencyUtils';

/**
 *
 * @param {Object} props - The component props.
 * @param {Availability} props.appointment - The list of appointments.
 * @param {Array<VirtualAvailability>} props.availabilities - The list of virtual availabilities.
 * @param {Clinic} props.organization - The organization object.
 * @param {GeoLocation} props.location - The location object.
 * @param {Array<ProcedureWithAddOns | ProcedureAddOn>} props.procedures - The list of procedures.
 * @param {AppointmentType} props.appointmentType - The appointment type object.
 * @returns
 */
const SummaryBooking = ({
  appointmentType,
  organization,
  location,
  procedures = [],
  appointment,
  availabilities = []
}) => {
  const [block, element] = bemify('summary-booking');
  const [_isExpanded, setIsExpanded] = useState(false);

  const bookedProceduresQuery = useQuery({
    placeholderData: [],
    queryKey: ['parse-selected-procedures', procedures],
    queryFn: ({ queryKey }) => {
      const [_, procedures] = queryKey;
      return parseSelectedProcedures(procedures);
    }
  });

  /** @param {SelectedProcedure} procedure */
  const parseProcedureText = procedure => {
    if (!procedure.addOns.length) {
      return procedure.name;
    }

    const addOnsText = `+ ${procedure.addOns.length === 1 ? '1 add-on' : `${procedure.addOns.length} add-ons`}`;

    return `${procedure.name} ${addOnsText}`;
  };

  const moreThanOneProcedure = bookedProceduresQuery.data?.length > 1;

  // Filter out add-ons from the top-level procedures list for cart items display
  // Add-ons have a procedureId field that references their parent procedure
  const mainProcedures = procedures?.filter(procedure => !procedure.procedureId) || [];

  return (
    <div className={block()}>
      <Accordion>
        <AccordionPanel
          defaultExpanded={false}
          onChange={setIsExpanded}
          label={
            <div className={element('accordion-header')}>
              <div className={element('card')}>
                <div className={element('appointment-header')}>
                  {!availabilities.length && (
                    <p className={element('appointment')}>
                      {new DateTime(appointment.date).format('EEEE, MMMM dd, yyyy')} at {appointment.time}
                    </p>
                  )}

                  {Boolean(availabilities.length) && (
                    <React.Fragment>
                      {availabilities.map((availability, index) => (
                        <p
                          key={index}
                          className={element('appointment')}>
                          {new DateTime(availability.date).format('EEEE, MMMM dd, yyyy')} in the&nbsp;
                          {listFormat(availability.timesOfDay, { title: true, type: 'disjunction' })}
                        </p>
                      ))}
                    </React.Fragment>
                  )}
                </div>
              </div>

              <Divider />

              {!isLoadingQuery(bookedProceduresQuery) && (
                <div className={element('procedures')}>
                  {bookedProceduresQuery.data.find(p => p.thumbnail)?.thumbnail && (
                    <div className={element('preview-container')}>
                      <div className={element('procedure-preview')}>
                        {moreThanOneProcedure && <div className={element('condensed-preview')} />}
                        <ImageAsync src={bookedProceduresQuery.data.find(p => p.thumbnail).thumbnail} />
                      </div>
                    </div>
                  )}

                  <div className={element('procedures-content')}>
                    <div className={element('procedures-header')}>
                      {listFormat(
                        bookedProceduresQuery.data.map(p => parseProcedureText(p)),
                        {
                          title: true,
                          type: 'conjunction'
                        }
                      ).replace(/\band\b/, '&')}
                    </div>

                    {(organization || location) && (
                      <div className={element('location-header')}>
                        {organization && appointmentType === 'clinic' && (
                          <div className={element('location')}>
                            <h3 className={element('organization')}>{organization.name}</h3>
                            <address className={element('address')}>{organization.address}</address>
                          </div>
                        )}
                        {location && appointmentType === 'home' && (
                          <div>
                            <h3 className={element('organization')}>Home</h3>
                            <address className={element('address')}>{location.formattedAddress}</address>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          }>
          <div className={element('cart-items')}>
            {mainProcedures.map((procedure, index) => (
              <React.Fragment key={`${procedure.id}-${index}`}>
                <div className={element('cart-item')}>
                  <h3 className={element('cart-item-name')}>{procedure.name}</h3>
                  <span className={element('cart-item-price')}>
                    {formatCurrency(Math.min(...procedure.price), { numDigits: 2 })}
                  </span>
                </div>

                {procedure.addOns
                  ?.filter(addOn => addOn.selected)
                  .map((addOn, addOnIndex) => (
                    <div
                      key={`${procedure.id}-addon-${addOnIndex}`}
                      className={element('cart-item', 'addon')}>
                      <h3 className={element('cart-item-name', 'addon')}>+ {addOn.name}</h3>
                      <span className={element('cart-item-price')}>
                        {formatCurrency(Math.min(...addOn.price), { numDigits: 2 })}
                      </span>
                    </div>
                  ))}
              </React.Fragment>
            ))}
          </div>
        </AccordionPanel>
      </Accordion>
    </div>
  );
};

export default SummaryBooking;
