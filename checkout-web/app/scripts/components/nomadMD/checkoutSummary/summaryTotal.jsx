import React from 'react';
import bemify from '../../../utils/bemUtils';
import { formatCurrency } from '../../../utils/currencyUtils';

const SummaryTotal = ({ procedures, membership, credit, discount, gratuity, travelFee, total }) => {
  const [block, element] = bemify('summary-total');

  // Filter out add-ons from the top-level procedures list
  // Add-ons have a procedureId field that references their parent procedure
  const mainProcedures = procedures?.filter(procedure => !procedure.procedureId) || [];

  return (
    <div className={block()}>
      {mainProcedures.map((procedure, index) => (
        <React.Fragment key={`${procedure.id}-${index}`}>
          <div className={element('price-section')}>
            <h3 className={element('heading')}>{procedure.name}</h3>
            <span className={element('value')}>{formatCurrency(Math.min(...procedure.price), { numDigits: 2 })}</span>
          </div>

          {procedure.addOns
            ?.filter(addOn => addOn.selected)
            .map((addOn, addOnIndex) => (
              <div
                key={`${procedure.id}-addon-${addOnIndex}`}
                className={element('price-section', 'addon')}>
                <h3 className={element('heading', 'addon')}>+ {addOn.name}</h3>
                <span className={element('value')}>{formatCurrency(Math.min(...addOn.price), { numDigits: 2 })}</span>
              </div>
            ))}
        </React.Fragment>
      ))}

      {Boolean(membership) && (
        <div className={element('price-section')}>
          <h3 className={element('heading')}>Membership</h3>
          <span className={element('value')}>{formatCurrency(membership, { numDigits: 2 })}</span>
        </div>
      )}

      {Boolean(credit) && (
        <div className={element('price-section')}>
          <h3 className={element('heading')}>Package Credit</h3>
          <span className={element('value')}>-{formatCurrency(credit, { numDigits: 2 })}</span>
        </div>
      )}

      {Boolean(discount) && (
        <div className={element('price-section')}>
          <h3 className={element('heading')}>Membership Discount</h3>
          <span className={element('value')}>-{formatCurrency(discount, { numDigits: 2 })}</span>
        </div>
      )}

      {Boolean(gratuity) && (
        <div className={element('price-section')}>
          <h3 className={element('heading')}>Gratuity</h3>
          <span className={element('value')}>{formatCurrency(gratuity, { numDigits: 2 })}</span>
        </div>
      )}

      {Boolean(travelFee) && (
        <div className={element('price-section')}>
          <h3 className={element('heading')}>Travel Fee</h3>
          <span className={element('value')}>{formatCurrency(travelFee, { numDigits: 2 })}</span>
        </div>
      )}

      <div className={element('price-section')}>
        <h3 className={element('heading', 'bold')}>Total</h3>
        <span className={element('heading', 'bold')}>{formatCurrency(total, { numDigits: 2 })}</span>
      </div>
    </div>
  );
};

export default SummaryTotal;
