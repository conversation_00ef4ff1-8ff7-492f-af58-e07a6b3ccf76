import React, { useEffect, useState, forwardRef, useRef, useImperativeHandle } from 'react';

import Dialog from '../dialog/dialog';
import ToastSystem from '../../../toast';
import bemify from '../../../utils/bemUtils';
import FinixForm from '../finixForm/finixForm';
import Button from '../../controls/button/button';
import { useMutation } from '@tanstack/react-query';
import NomadMDService from '../../../services/nomadService';
import { isLoadingMutation } from '../../../utils/networkUtils';
import SummaryPaymentInstrument from './summaryPaymentInstrument';
import ApplePay from '../applePay/applePay';

/**
 * Represents the payment section of the checkout summary.
 * @param {Object} props - The component props.
 * @param {number} props.total - The total amount to be paid.
 * @param {PaymentInstrument?} props.paymentInstrument - The payment instrument object.
 * @param {UserProfile} props.profile - The user profile object.
 * @param {Array<PaymentInstrument>} props.paymentInstruments - The list of payment instruments.
 * @param {Function} props.onChangeMethod - Callback function to change the payment method.
 * @param {Function} props.onTokenGenerated - Callback function when token is generated for unauthenticated users.
 * @param {boolean} props.isAuthenticated - Whether the user is authenticated.
 * @remarks This content will be a container for the iframed content.
 */
const SummaryPayment = forwardRef(
  (
    {
      total,
      profile,
      paymentInstrument,
      paymentInstruments = [],
      onChangeMethod,
      onTokenGenerated,
      isAuthenticated = true,
      paymentCaption
    },
    ref
  ) => {
    const [block, element] = bemify('summary-payment');

    const finixFormRef = useRef(null);

    const [changeMethod, setChangeMethod] = useState(false);

    const [errors, setErrors] = useState([]);

    const [paymentType, setPaymentType] = useState();

    const [showDefaultPayment, setShowDefaultPayment] = useState(false);

    const [registerPaymentInline, setRegisterPaymentInline] = useState(false);

    const [cameFromAddCard, setCameFromAddCard] = useState(false);

    const updatePaymentInstrumentMutation = useMutation({
      mutationFn: NomadMDService.updatePaymentInstrument.function,
      onSettled: () => setRegisterPaymentInline(false)
    });

    const addPaymentInstrumentMutation = useMutation({
      mutationFn: NomadMDService.addPaymentInstrument.function,
      onSuccess: paymentInstrument => {
        ToastSystem.show('Card added successfully', { type: 'success' });

        if (paymentInstrument) {
          updatePaymentInstrumentMutation.mutate({
            id: paymentInstrument.id
          });
        } else {
          setRegisterPaymentInline(false);
        }
        setCameFromAddCard(false);
      }
    });

    useEffect(() => {
      if (paymentInstrument) {
        setRegisterPaymentInline(false);
        setShowDefaultPayment(true);
        setCameFromAddCard(false);
      } else {
        setShowDefaultPayment(false);
        setRegisterPaymentInline(true);
      }
    }, [paymentInstrument]);

    const sortedPaymentInstruments = paymentInstruments.sort(a => {
      if (a.id === paymentInstrument?.id) {
        return -1;
      }
      return 0;
    });

    const handleTokenGenerated = token => {
      if (isAuthenticated) {
        addPaymentInstrumentMutation.mutate({ token, profile });
      } else {
        const identity = {
          firstName: profile?.givenName,
          lastName: profile?.familyName,
          email: profile?.email,
          phone: profile?.phone,
          postalCode: profile?.address?.postalCode
        };
        onTokenGenerated?.({ token, identity });
      }
    };

    useImperativeHandle(ref, () => ({
      generateToken: async () => {
        if (finixFormRef.current) {
          const token = await finixFormRef.current.generateToken();
          if (!isAuthenticated) {
            const identity = {
              firstName: profile?.givenName,
              lastName: profile?.familyName,
              email: profile?.email,
              phone: profile?.phone,
              postalCode: profile?.address?.postalCode
            };
            const tokenData = { token, identity };
            return tokenData;
          }
          return token;
        }
        throw new Error('Payment form not ready');
      }
    }));

    return (
      <div className={block()}>
        <div className={element('header')}>
          <h3 className={element('heading', 'bold')}>Payment</h3>
          {paymentCaption && <p className={element('caption')}>{paymentCaption}</p>}
        </div>

        {/* Payment method selection for unauthenticated users */}
        {!paymentInstrument && (
          <div className={element('payment-selection')}>
            <div
              className={element('payment-option', paymentType === 'credit' ? 'selected' : '')}
              onClick={() => {
                setPaymentType('credit');
                setShowDefaultPayment(false);
                setRegisterPaymentInline(true);
                setCameFromAddCard(false);
              }}>
              <span className="material-symbols-outlined">credit_card</span>
              <p>Credit or Debit Card</p>
            </div>

            <ApplePay
              profile={profile}
              amount={total}
              onSuccess={paymentInstrument => {
                setPaymentType('apple-pay');
                setShowDefaultPayment(false);
                setRegisterPaymentInline(false);
                setCameFromAddCard(false);
                onChangeMethod(paymentInstrument);
              }}
              onUnAvailable={() => {
                setPaymentType('credit');
              }}
              classes={element('payment-option', ['apple-pay', paymentType === 'apple-pay' ? 'selected' : ''])}
              onClick={() => {
                setErrors([]);
                setPaymentType('apple-pay');
                setCameFromAddCard(false);
              }}
              setErrors={errors => {
                setErrors(errors);
              }}
            />
          </div>
        )}

        {Boolean(errors.length) && (
          <div className={element('error')}>
            {errors.map((err, index) => (
              <div
                key={`error-${index}`}
                className={element('error-item')}>
                <span className="material-symbols-outlined">error</span>
                <span>{err}</span>
              </div>
            ))}
          </div>
        )}

        {/* Authenticated user with existing payment method */}
        {showDefaultPayment && paymentInstrument && (
          <div className={element('authenticated-payment')}>
            <SummaryPaymentInstrument
              actionLabel="Change method"
              onChangeMethod={() => {
                setChangeMethod(true);
              }}
              paymentInstrument={paymentInstrument}
            />
          </div>
        )}

        {paymentType === 'credit' && registerPaymentInline && (
          <FinixForm
            ref={finixFormRef}
            idForm="finix-form-checkout"
            error={addPaymentInstrumentMutation.error}
            loading={isLoadingMutation(addPaymentInstrumentMutation, updatePaymentInstrumentMutation)}
            onToken={handleTokenGenerated}
            buttonText={isAuthenticated ? 'Save Card' : 'Continue'}
            hideSubmitButton={!isAuthenticated}
            onClose={
              isAuthenticated && cameFromAddCard
                ? () => {
                    setRegisterPaymentInline(false);
                    setShowDefaultPayment(true);
                    setPaymentType(undefined);
                    setCameFromAddCard(false);
                  }
                : undefined
            }
          />
        )}

        <Dialog
          open={changeMethod}
          overlayClasses={element('overlay')}
          classes={element('dialog')}
          setOpen={setChangeMethod}>
          <Dialog.Header>Change Payment Method</Dialog.Header>
          <Dialog.Body>
            <div className={element('list')}>
              {sortedPaymentInstruments.map(payment => (
                <SummaryPaymentInstrument
                  key={payment.id}
                  actionLabel={payment.id === paymentInstrument?.id ? 'Selected' : 'Select'}
                  paymentInstrument={payment}
                  onChangeMethod={() => {
                    setChangeMethod(false);
                    setCameFromAddCard(false);
                    onChangeMethod(payment);
                  }}
                />
              ))}

              <div className={element('options')}>
                <div
                  className={element('add-card-option')}
                  onClick={() => {
                    setChangeMethod(false);
                    setRegisterPaymentInline(true);
                    setShowDefaultPayment(false);
                    setPaymentType('credit');
                    setCameFromAddCard(true);
                  }}>
                  <span className="material-symbols-outlined">add</span>
                  <span>Add Credit or Debit Card</span>
                </div>

                <ApplePay
                  profile={profile}
                  amount={total}
                  onSuccess={paymentInstrument => {
                    setChangeMethod(false);
                    setCameFromAddCard(false);
                    onChangeMethod(paymentInstrument);
                  }}
                  classes={element('apple-pay-dialog-option')}
                  setErrors={setErrors}
                />
              </div>
            </div>
            <div className={element('actions-buttons')}>
              <Button
                variant="secondary"
                onClick={() => setChangeMethod(false)}>
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={() => setChangeMethod(false)}>
                Done
              </Button>
            </div>
          </Dialog.Body>
          <Dialog.Footer></Dialog.Footer>
        </Dialog>
      </div>
    );
  }
);

SummaryPayment.displayName = 'SummaryPayment';

export default SummaryPayment;
