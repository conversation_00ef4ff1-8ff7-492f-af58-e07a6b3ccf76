import React from 'react';
import bemify from '../../../utils/bemUtils';
import { formatCurrency } from '../../../utils/currencyUtils';

const SummaryDueNow = ({ dueNow }) => {
  const [block, element] = bemify('summary-due-now');

  return (
    <div className={block()}>
      <div className={element('price-section')}>
        <h3 className={element('heading', 'bold')}>Due Now</h3>
        <span className={element('heading', 'bold')}>{formatCurrency(dueNow, { numDigits: 2 })}</span>
      </div>
    </div>
  );
};

export default SummaryDueNow;
