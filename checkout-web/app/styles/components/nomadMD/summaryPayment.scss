@use '../../layers';
@use '../../screens';
@use '../../typography';

.summary-payment {
  margin-bottom: 1em;
  padding-inline: 1rem;

  .finix-form-container {
    margin-top: 1rem;

    .field-holder {
      margin-top: 0.5rem !important;
    }
    .field-array {
      margin-top: 0 !important;
    }
    .field {
      margin-top: 0.25rem !important;
    }
  }
}

.summary-payment__svg-icon {
  height: 1.5rem;
  fill: var(--sk-theme-primary);
}

.summary-payment__overlay {
  z-index: layers.$layer-3;
}

//header
.summary-payment__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.summary-payment__heading {
  @extend .text;

  margin-bottom: 0.5rem;
}

.summary-payment__heading--bold {
  @extend .text--boldest;
}

.summary-payment__caption {
  @extend .text--small;
  @extend .text--leading-lg;
  margin-top: 0.25rem;
  margin-bottom: 0;
  color: var(--sk-theme-text-secondary);
}

//payment-instrument
.summary-payment__payment-instrument {
  display: flex;
  padding: 1em;
  margin-top: 0.5em;
  border-radius: 8px;
  align-items: center;
  justify-content: space-between;

  background-color: var(--sk-theme-surface);
  border: 1px solid var(--sk-theme-grey-lighter);
}

.summary-payment__info-section {
  display: flex;
  column-gap: 0.5rem;
  align-items: center;
}

.summary-payment__action-link {
  @extend .text--x-small;
  @extend .text--medium;

  margin: 0;
  padding: 0;
  border: none;
  height: auto;
  display: flex;
  cursor: pointer;
  border-radius: 0;
  text-decoration: underline;
  align-items: center;
  justify-content: flex-end;
}

//add-payment
.summary-payment__add-payment {
  @extend .text--small;
}

//actions-buttons
.summary-payment__actions-buttons {
  display: flex;
  flex-direction: column;
  margin-top: 1rem;
  width: 100%;
  gap: 1em;

  @media (min-width: screens.$screen-md) {
    flex-direction: row;
    justify-content: flex-end;
  }
}

//no-payment
.summary-payment__no-payment {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  gap: 0.5rem;
  margin-top: 0.5rem;
  margin-bottom: 1rem;
}

//payment-option
.summary-payment__payment-option {
  width: 160px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;

  border: 1px solid var(--sk-theme-grey-lighter);
  border-radius: 3px;

  cursor: pointer;
  transition: background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;

  p {
    @extend .text--x-small;
  }
}

.summary-payment__payment-option:hover {
  border-color: var(--sk-theme-primary);
}

.summary-payment__payment-option--apple-pay {
  img {
    width: auto;
    height: 1rem;
  }
}

.summary-payment__payment-option--selected {
  background-color: var(--sk-theme-surface-highlight);
  border-color: var(--sk-theme-black);
}

.summary-payment__error {
  display: flex;
  flex-direction: column;
  gap: 1em;
}

.summary-payment__error-item {
  width: 100%;
  display: flex;
  column-gap: 0.5em;

  padding: 1em;
  border-radius: 8px;
  border: 1px solid var(--sk-theme-error);
  background: var(--sk-theme-error-light);

  span {
    @extend .text--normal;
    color: var(--sk-theme-error);
  }
}

// New payment selection styles
.summary-payment__payment-selection {
  display: flex;
  gap: 0.75rem;
  margin-top: 0.75rem;

  @media (max-width: screens.$screen-sm) {
    flex-direction: column;
  }
}

.summary-payment__authenticated-payment {
  margin-top: 0.75rem;
}

// Add card option in dialog
.summary-payment__add-card-option {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  border: 1px solid var(--sk-theme-grey-lighter);
  border-radius: 8px;
  cursor: pointer;
  margin-bottom: 0.75rem;

  &:hover {
    background-color: var(--sk-theme-grey-lightest);
  }

  span:first-child {
    color: var(--sk-theme-primary);
  }

  span:last-child {
    @extend .text--small;
    @extend .text--medium;
  }
}

.summary-payment__options {
  display: flex;
  flex-direction: row;
  gap: 0.75rem;
  margin-top: 0.75rem;

  div {
    flex: 1;
  }
}

.summary-payment__apple-pay-dialog-option {
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  border: 1px solid var(--sk-theme-grey-lighter);
  border-radius: 8px;
  cursor: pointer;
  margin-bottom: 0.75rem;

  &:hover {
    background-color: var(--sk-theme-grey-lightest);
  }

  span:first-child {
    color: var(--sk-theme-primary);
  }

  span:last-child {
    @extend .text--small;
    @extend .text--medium;
  }
  img {
    width: auto;
    height: 1.2rem;
    margin: 0 auto;
  }  
}
