@use '../../screens';
@use '../../typography';

.summary-booking {
  margin-bottom: 1em;

  .link {
    @extend .text--small;
  }

  .accordion {
    border-radius: 0;
    box-shadow: none;
  }

  .accordion__panel {
    border: 1px solid var(--sk-theme-grey-lighter);
    border-radius: 8px;
    margin-bottom: 0;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .accordion__panel--expanded {
    .accordion__content {
      padding: 1em;
      background-color: var(--sk-theme-grey-lightest);
    }
  }

  .accordion__toggle {
    &:focus {
      ~ .accordion__header {
        background-color: var(--sk-theme-white);
      }
    }
  }

  .accordion__header {
    padding: 0;
    background-color: var(--sk-theme-white);

    &:focus {
      background-color: var(--sk-theme-white);
    }
  }

  .accordion__label {
    line-height: inherit !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .accordion__content {
    padding: 0;
  }
}

.summary-booking__header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.summary-booking__heading {
  @extend .text;
}

.summary-booking__heading--bold {
  @extend .text--boldest;
}

.summary-booking__card {
  padding-block: 0.5rem;
  padding-inline: 1rem;

  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 1.5rem;

  &:last-child {
    margin-bottom: 0;
  }
}

.summary-booking__appointment-header {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
}

//location-header
.summary-booking__location-header {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.summary-booking__location {
  display: flex;
  margin-top: 0.4rem;
  flex-direction: column;
  gap: 0.25rem;
}

//organization
.summary-booking__organization {
  @extend .text--x-small;
}

//address
.summary-booking__address {
  @extend .text--x-small;
  @extend .text--leading-md;
}

.summary-booking__header-section {
  display: flex;
  justify-content: space-evenly;
  align-items: stretch;
  position: relative;
  flex-wrap: wrap;

  margin-top: 0.5rem;

  @media (min-width: screens.$screen-md) {
    min-width: 50px;
    margin-top: 0;
    height: 50px;
  }
}

.summary-booking__header-item {
  display: flex;
  justify-content: center;
  align-items: center;

  flex-basis: calc(100%);

  @media (min-width: screens.$screen-md) {
    flex-basis: calc(50% - 0.5rem);
  }
}

.summary-booking__appointment {
  @extend .text--normal;
  @extend .text--boldest;
}

//appointment-virtual
.summary-booking__appointment-virtual {
  @extend .text--small;

  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.5rem;
  gap: 1rem;
}

.summary-booking__divider {
  height: 1px;
  width: 100%;

  @media (min-width: screens.$screen-md) {
    border: none;
    border-right: 1px solid var(--sk-theme-grey-lighter);
    align-self: stretch;
    background: none;
    margin: 0;
    height: 100%;
    width: 1px;
  }
}

.summary-booking__procedures {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 0.5rem;

  padding-inline: 1rem;
}

//procedure
.summary-booking__procedure {
  @extend .text;
  @extend .text--small;

  display: flex;
  box-sizing: border-box;
  justify-content: space-between;

  border: 1px solid var(--sk-theme-grey-lighter);
  border-radius: 3px;
  padding: 1rem;

  flex-basis: calc(50% - 0.25rem);

  @media (min-width: screens.$screen-lg) {
    flex-basis: calc(33% - 0.25rem);
  }
}

//procedure-preview
.summary-booking__preview-container {
  position: relative;

  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.5rem;

  img {
    padding: 0.25rem;
    height: auto;
    width: 4rem !important;
    z-index: 1;
    aspect-ratio: 1 / 1;
    border-radius: 8px;
    background-color: var(--sk-theme-white) !important;
  }
}

//procedure-preview
.summary-booking__procedure-preview {
  border: 1px solid var(--sk-theme-grey-light);
  border-radius: 8px;
  background-color: var(--sk-theme-white) !important;
}

//condensed-preview
.summary-booking__condensed-preview {
  position: absolute;

  bottom: -0.25rem;
  right: -0.25rem;
  aspect-ratio: 1 / 1;
  width: 4rem;

  border: 1px solid var(--sk-theme-grey-light);
  border-radius: 8px;
  z-index: 0;
}

.summary-booking__empty-placeholder {
  width: 4rem;
  height: 4rem;
  aspect-ratio: 1 / 1;
}

// Accordion header styling
.summary-booking__accordion-header {
  width: 100%;

  // Ensure text elements maintain their original styling
  .summary-booking__appointment {
    line-height: inherit !important;
  }

  .summary-booking__organization {
    line-height: inherit !important;
  }

  .summary-booking__address {
    line-height: inherit !important;
  }

  .summary-booking__procedures-header {
    line-height: inherit !important;
  }
}

// Cart items styling
.summary-booking__cart-items {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.summary-booking__cart-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;

  &:not(:last-child) {
    border-bottom: 1px solid var(--sk-theme-grey-lighter);
  }

  &--addon {
    padding-left: 1rem;
    font-size: 0.9rem;
  }
}

.summary-booking__cart-item-name {
  @extend .text--small;
  @extend .text--medium;
  margin: 0;
  flex: 1;

  &--addon {
    @extend .text--x-small;
    color: var(--sk-theme-grey);
  }
}

.summary-booking__cart-item-price {
  @extend .text--small;
  @extend .text--medium;
  color: var(--sk-theme-text);
}
