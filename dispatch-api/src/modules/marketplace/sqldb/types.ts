import {
  FilterNumberFields,
  FilterStringFields,
  SortDirection,
} from '../../common/sqldb/types';

export interface MarketplaceFields {
  name?: string;
  groupId?: number;
  navigationGroupId?: number;
  reviewsGroupId?: number;
  notificationsGroupId?: number;
  requireDispatchApproval?: boolean;
  requirePractitionerApproval?: boolean;
  slackWebhookUrl?: string;
  reviewsIoKey?: string; // deprecated - will be removed
  reviewsIoKeyId?: number;
  reviewsIoStoreId?: string;
  feeProfileFixed?: number;
  feeProfileBasisPoints?: number;
  logo?: string | null;
  primaryColor?: string | null;
  paymentCollectionMethod?: string;
  paymentDepositType?: string;
  paymentDepositValue?: number;
  hasPaymentPolicy?: boolean;
  paymentPolicyName?: string;
  paymentPolicyText?: string;
  requirePaymentPolicyAttestation?: boolean;
}

export interface MarketplaceGroupFields {
  label?: string;
}

export enum MarketplaceUserSortField {
  ID = 'id',
  EMAIL = 'email',
  GIVENNAME = 'givenName',
  FAMILYNAME = 'familyName',
  FULLNAME = 'fullName',
  PHONE = 'phone',
  ADDRESS = 'address',
  DOB = 'dob',
  CLIENTPROFILEID = 'clientProfileId',
  MEMBERSHIP = 'membership',
  MEMBERSHIPS = 'memberships',
  CREATEDAT = 'createdAt',
  UPDATEDAT = 'updatedAt',
}

export interface MarketplaceUserSortFields {
  field: MarketplaceUserSortField;
  direction: SortDirection;
}

export interface MarketplaceUserFields {
  groupId?: number;
  email?: string;
  emailConfirmed?: boolean;
  phone?: string;
  phoneConfirmed?: boolean;
  phoneOptIn?: boolean;
  emailOptIn?: boolean;
}

export interface MarketplaceUserFilterFields {
  id?: FilterNumberFields;
  email?: FilterStringFields;
  givenName?: FilterStringFields;
  familyName?: FilterStringFields;
  fullName?: FilterStringFields;
  phone?: FilterStringFields;
  address?: FilterStringFields;
  membership?: FilterStringFields; // legacy
  memberships?: FilterStringFields;
  clientProfileId?: FilterNumberFields;
  groupId?: FilterNumberFields;
}
