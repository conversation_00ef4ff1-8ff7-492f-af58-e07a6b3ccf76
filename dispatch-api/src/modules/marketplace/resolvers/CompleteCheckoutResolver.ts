import { ApolloError, ForbiddenError } from 'apollo-server';
import { Arg, Ctx, Mutation, Resolver } from 'type-graphql';
import { ResolverContext } from '../../../api/context';
import { CurrentUser, intFromID } from '../../common/type-graphql';
import {
  authorizeCheckout,
  authorizePaymentMethod,
} from '../../payment/common';
import { User } from '../../user/sqldb';
import { completeCheckout } from '../checkout';
import CompleteCheckoutInput from './CompleteCheckoutInput';
import CompleteCheckoutPayload from './CompleteCheckoutPayload';

@Resolver()
export default class CompleteCheckoutResolver {
  @Mutation(() => CompleteCheckoutPayload)
  async completeCheckout(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Arg('input') input: CompleteCheckoutInput,
  ): Promise<CompleteCheckoutPayload> {
    const checkout = await sqldb.checkout(intFromID(input.checkoutId));

    if (!checkout) {
      throw new ApolloError(
        'Checkout does not exist for appointment',
        'complete-appointment-checkout:checkout',
      );
    }

    if (!authorizeCheckout(user, checkout.id, { sqldb })) {
      throw new ForbiddenError('Not authorized (completeCheckout)');
    }

    const paymentMethod = input.paymentMethod?.toPaymentMethod();

    if (
      paymentMethod &&
      !(await authorizePaymentMethod(user, paymentMethod, { sqldb }))
    ) {
      throw new ForbiddenError('Not authorized (completeCheckout)');
    }

    const { expectedAmount, isAdminPayment } = input;

    try {
      const result = await completeCheckout({
        sqldb,
        checkoutId: checkout.id,
        paymentMethod,
        ...(expectedAmount && { expectedAmount }),
        ...(isAdminPayment && { isAdminPayment }),
      });

      return {
        success: true,
        checkout: result,
      };
    } catch (err) {
      return {
        success: false,
        checkout: (await sqldb.checkout(checkout.id)) ?? checkout,
        errorMessage: (err as Error).message || 'An error occurred',
      };
    }
  }
}
