import { ForbiddenError } from 'apollo-server';
import { Page } from 'objection';
import {
  Arg,
  Ctx,
  FieldResolver,
  ID,
  Int,
  Query,
  Resolver,
  Root,
} from 'type-graphql';
import { ResolverContext } from '../../../api/context';
import { AppointmentRequestPage } from '../../appointment/AppointmentRequestPage';
import AppointmentRequestPageInput from '../../appointment/resolvers/AppointmentRequestPageInput';
import { AppointmentRequest } from '../../appointment/sqldb';
import { getPaginatedAppointmentRequests } from '../../appointment/sqldb/queries';
import { CurrentUser, intFromID } from '../../common/type-graphql';
import { MembershipDefinition, Package } from '../../membership/sqldb';
import {
  getMarketplaceMembershipDefinitions,
  getMarketplacePackages,
} from '../../membership/sqldb/queries';
import { Organization } from '../../organization/sqldb';
import { getOrganizationsByMarketplace } from '../../organization/sqldb/queries';
import { ProcedureBaseDefinitionGroup } from '../../procedure-base-def-group/sqldb';
import { getMarketplaceProcedureBaseDefGroups } from '../../procedure-base-def-group/sqldb/queries';
import { ProcedureBaseDefinition } from '../../procedure/sqldb';
import { getProcedureBaseDefsByMarketplace } from '../../procedure/sqldb/queries';
import Role, { RoleScope } from '../../role/sqldb/Role';
import { authorize } from '../../user/authorize';
import { User } from '../../user/sqldb';
import { Marketplace } from '../sqldb';
import MarketplaceGroup from '../sqldb/MarketplaceGroup';
import { getMarketplace, getMarketplaces } from '../sqldb/queries';
import { AttentiveIntegration } from '../../attentive/sqldb';
import {
  SegmentIntegration,
  SendgridIntegration,
  TwilioIntegration,
} from '../../twilio/sqldb';
import { PaymentAccount } from '../../payment/sqldb';

@Resolver(() => Marketplace)
export default class MarketplaceResolver {
  @FieldResolver(() => Boolean)
  requireDispatchApproval(@Root() marketplace: Marketplace): boolean {
    return marketplace.requireDispatchApproval || false;
  }

  @FieldResolver(() => Boolean)
  requirePractitionerApproval(@Root() marketplace: Marketplace): boolean {
    return marketplace.requirePractitionerApproval || false;
  }

  @FieldResolver(() => String, { nullable: true })
  logo(@Root() marketplace: Marketplace): string | null {
    return marketplace.logo || null;
  }

  @FieldResolver(() => String, { nullable: true })
  primaryColor(@Root() marketplace: Marketplace): string | null {
    return marketplace.primaryColor || null;
  }

  @FieldResolver(() => String, { nullable: true })
  paymentCollectionMethod(@Root() marketplace: Marketplace): string | null {
    return marketplace.paymentCollectionMethod || 'collect_on_confirmation';
  }

  @FieldResolver(() => String, { nullable: true })
  paymentDepositType(@Root() marketplace: Marketplace): string | null {
    return marketplace.paymentDepositType || null;
  }

  @FieldResolver(() => Number, { nullable: true })
  paymentDepositValue(@Root() marketplace: Marketplace): number | null {
    return marketplace.paymentDepositValue || null;
  }

  @FieldResolver(() => Boolean, { nullable: true })
  hasPaymentPolicy(@Root() marketplace: Marketplace): boolean | null {
    return marketplace.hasPaymentPolicy || false;
  }

  @FieldResolver(() => String, { nullable: true })
  paymentPolicyName(@Root() marketplace: Marketplace): string | null {
    return marketplace.paymentPolicyName || null;
  }

  @FieldResolver(() => String, { nullable: true })
  paymentPolicyText(@Root() marketplace: Marketplace): string | null {
    return marketplace.paymentPolicyText || null;
  }

  @FieldResolver(() => Boolean, { nullable: true })
  requirePaymentPolicyAttestation(
    @Root() marketplace: Marketplace,
  ): boolean | null {
    return marketplace.requirePaymentPolicyAttestation || false;
  }

  @FieldResolver(() => [Organization])
  async organizations(
    @CurrentUser() user: User,
    @Ctx() { dataSources }: ResolverContext,
    @Root() marketplace: Marketplace,
  ): Promise<Organization[]> {
    if (
      !authorize(user, ['organizations:list', 'organizations:full']) &&
      !authorize(
        user,
        ['marketplace.organizations:list', 'marketplace.organizations:full'],
        {
          scope: RoleScope.MARKETPLACE,
          resourceId: marketplace.id,
        },
      )
    ) {
      throw new ForbiddenError('Not authorized (marketplace/organizations)');
    }

    return (
      marketplace.organizations ||
      getOrganizationsByMarketplace(dataSources.sqldb.knex, {
        marketplaceId: marketplace.id,
      })
    );
  }

  @FieldResolver(() => [Role])
  async roles(
    @CurrentUser() user: User,
    @Ctx() { dataSources }: ResolverContext,
    @Root() marketplace: Marketplace,
  ): Promise<Role[]> {
    if (
      !authorize(user, ['roles:full', 'roles:list']) &&
      !authorize(user, ['marketplace.roles:full', 'marketplace.roles:list'], {
        scope: RoleScope.MARKETPLACE,
        resourceId: marketplace.id,
      })
    ) {
      // throw new ForbiddenError('Not authorized (marketplace/roles)');
      return [];
    }

    return (
      marketplace.roles ||
      dataSources.sqldb.roles(RoleScope.MARKETPLACE, marketplace.id)
    );
  }

  @FieldResolver(() => [ProcedureBaseDefinition])
  async procedureBaseDefs(
    @CurrentUser() user: User,
    @Ctx() { dataSources }: ResolverContext,
    @Root() marketplace: Marketplace,
  ): Promise<ProcedureBaseDefinition[]> {
    return (
      marketplace.procedureBaseDefs ||
      getProcedureBaseDefsByMarketplace(dataSources.sqldb.knex, {
        marketplaceId: marketplace.id,
      })
    );
  }

  @FieldResolver(() => [ProcedureBaseDefinitionGroup])
  async procedureBaseDefGroups(
    @CurrentUser() user: User,
    @Ctx() { dataSources }: ResolverContext,
    @Root() marketplace: Marketplace,
  ) {
    return (
      marketplace.procedureBaseDefGroups ||
      getMarketplaceProcedureBaseDefGroups(dataSources.sqldb.knex, {
        id: marketplace.id,
      })
    );
  }

  @FieldResolver(() => MarketplaceGroup)
  async group(
    @CurrentUser() user: User,
    @Ctx() { dataSources }: ResolverContext,
    @Root() marketplace: Marketplace,
  ): Promise<MarketplaceGroup> {
    return (
      marketplace.group ||
      marketplace.$relatedQuery('group', dataSources.sqldb.knex)
    );
  }

  @FieldResolver(() => AppointmentRequestPage)
  async appointmentRequests(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Root() marketplace: Marketplace,
    @Arg('page', { defaultValue: {} }) page: AppointmentRequestPageInput,
  ): Promise<AppointmentRequestPage> {
    let result: Page<AppointmentRequest> = { total: 0, results: [] };

    if (
      authorize(user, ['appointments:full', 'appointments:list']) ||
      !authorize(
        user,
        ['marketplace.appointments:full', 'marketplace.appointments:list'],
        {
          scope: RoleScope.MARKETPLACE,
          resourceId: marketplace.id,
        },
      )
    ) {
      result = await getPaginatedAppointmentRequests(sqldb.knex, page)
        .where('marketplaceId', marketplace.id)
        .withGraphFetched(
          '[procedureBaseDefs, clientProfiles, constraints(ordered).timeRanges]',
        );
    }

    return {
      totalCount: result.total,
      data: result.results,
    };
  }

  @FieldResolver(() => [Package])
  async packages(
    @CurrentUser() user: User,
    @Ctx() { dataSources }: ResolverContext,
    @Root() marketplace: Marketplace,
  ) {
    if (
      authorize(user, ['marketplaces:full']) ||
      authorize(user, ['marketplace.update'], {
        scope: RoleScope.MARKETPLACE,
        resourceId: marketplace.id,
      })
    ) {
      return (
        marketplace.packages ||
        getMarketplacePackages(dataSources.sqldb.knex, {
          id: marketplace.id,
        })
      );
    }
    return [];
  }

  @FieldResolver(() => [MembershipDefinition])
  async membershipDefinitions(
    @CurrentUser() user: User,
    @Ctx() { dataSources }: ResolverContext,
    @Root() marketplace: Marketplace,
  ) {
    if (
      authorize(user, ['marketplaces:full']) ||
      authorize(user, ['marketplace.update'], {
        scope: RoleScope.MARKETPLACE,
        resourceId: marketplace.id,
      })
    ) {
      return (
        marketplace.membershipDefinitions ||
        getMarketplaceMembershipDefinitions(dataSources.sqldb.knex, {
          id: marketplace.id,
        })
      );
    }
    return [];
  }

  @FieldResolver(() => String, { nullable: true })
  slackWebhookUrl(
    @CurrentUser() user: User,
    @Root() marketplace: Marketplace,
  ): string | null {
    if (
      marketplace.slackWebhookUrl &&
      (authorize(user, ['marketplaces:full']) ||
        authorize(user, ['marketplace.update'], {
          scope: RoleScope.MARKETPLACE,
          resourceId: marketplace.id,
        }))
    ) {
      return marketplace.slackWebhookUrl;
    }
    return null;
  }

  @FieldResolver(() => String, { nullable: true })
  async reviewsIoKeyDescription(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Root() marketplace: Marketplace,
  ): Promise<string | null> {
    if (
      !authorize(user, ['marketplaces:full']) &&
      !authorize(user, 'marketplace:update', {
        scope: RoleScope.MARKETPLACE,
        resourceId: marketplace.id,
      })
    ) {
      return null;
    }

    const encryptedKey =
      marketplace.encryptedReviewsIoKey ||
      (await sqldb.encryptedKey(marketplace.reviewsIoKeyId));

    return encryptedKey ? encryptedKey?.description || '' : null;
  }

  @FieldResolver(() => Int, { nullable: true })
  async feeProfileFixed(
    @CurrentUser() user: User,
    @Root() marketplace: Marketplace,
  ): Promise<number | null> {
    if (
      authorize(user, ['marketplaces:full']) ||
      authorize(user, ['marketplace.update'], {
        scope: RoleScope.MARKETPLACE,
        resourceId: marketplace.id,
      })
    ) {
      return marketplace.feeProfileFixed || 0;
    }
    return null;
  }

  @FieldResolver(() => Int, { nullable: true })
  async feeProfileBasisPoints(
    @CurrentUser() user: User,
    @Root() marketplace: Marketplace,
  ): Promise<number | null> {
    if (
      authorize(user, ['marketplaces:full']) ||
      authorize(user, ['marketplace.update'], {
        scope: RoleScope.MARKETPLACE,
        resourceId: marketplace.id,
      })
    ) {
      return marketplace.feeProfileBasisPoints || 0;
    }
    return null;
  }

  @FieldResolver(() => AttentiveIntegration, { nullable: true })
  async attentive(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Root() marketplace: Marketplace,
  ): Promise<AttentiveIntegration | null | undefined> {
    if (
      !authorize(user, ['marketplaces:full']) &&
      !authorize(user, 'marketplace:update', {
        scope: RoleScope.MARKETPLACE,
        resourceId: marketplace.id,
      })
    ) {
      return null;
    }

    return (
      marketplace.attentive ||
      (await sqldb.attentiveIntegrationForMarketplace(marketplace.id))
    );
  }

  @FieldResolver(() => SendgridIntegration, { nullable: true })
  async sendgrid(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Root() marketplace: Marketplace,
  ): Promise<SendgridIntegration | null | undefined> {
    if (
      authorize(user, ['payments:full']) ||
      authorize(user, ['marketplace:update'], {
        scope: RoleScope.MARKETPLACE,
        resourceId: marketplace.id,
      })
    ) {
      return (
        marketplace.sendgrid ||
        (await sqldb.sendgridIntegrationForMarketplace(marketplace.id))
      );
    }

    return null;
  }

  @FieldResolver(() => SegmentIntegration, { nullable: true })
  async segment(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Root() marketplace: Marketplace,
  ): Promise<SegmentIntegration | null | undefined> {
    if (
      authorize(user, ['payments:full']) ||
      authorize(user, ['marketplace:update'], {
        scope: RoleScope.MARKETPLACE,
        resourceId: marketplace.id,
      })
    ) {
      return (
        marketplace.segment ||
        (await sqldb.segmentIntegrationForMarketplace(marketplace.id))
      );
    }

    return null;
  }

  @FieldResolver(() => [PaymentAccount])
  async paymentAccounts(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Root() marketplace: Marketplace,
  ): Promise<PaymentAccount[]> {
    if (
      authorize(user, ['payments:full']) ||
      authorize(user, ['marketplace:update'], {
        scope: RoleScope.MARKETPLACE,
        resourceId: marketplace.id,
      })
    ) {
      return (
        marketplace.paymentAccounts ||
        marketplace
          .$relatedQuery('paymentAccounts', sqldb.knex)
          .withGraphJoined('finix')
      );
    }

    return [];
  }

  @FieldResolver(() => TwilioIntegration, { nullable: true })
  async twilio(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Root() marketplace: Marketplace,
  ): Promise<TwilioIntegration | null | undefined> {
    if (
      authorize(user, ['payments:full']) ||
      authorize(user, ['marketplace:update'], {
        scope: RoleScope.MARKETPLACE,
        resourceId: marketplace.id,
      })
    ) {
      return (
        marketplace.twilio ||
        (await sqldb.twilioIntegrationForMarketplace(marketplace.id))
      );
    }

    return null;
  }

  @Query(() => [Marketplace])
  async marketplaces(
    @CurrentUser() user: User,
    @Ctx() { dataSources }: ResolverContext,
  ): Promise<Marketplace[]> {
    const marketplaces = await getMarketplaces(dataSources.sqldb.knex);

    if (authorize(user, ['marketplaces:full', 'marketplaces:list'])) {
      return marketplaces;
    }

    return marketplaces.filter((marketplace) =>
      authorize(user, [], {
        scope: RoleScope.MARKETPLACE,
        resourceId: marketplace.id,
      }),
    );
  }

  @Query(() => Marketplace, { nullable: true })
  async marketplace(
    @CurrentUser() user: User,
    @Ctx() { dataSources }: ResolverContext,
    @Arg('id', () => ID) id: string,
  ): Promise<Marketplace | null> {
    const marketplaceId = intFromID(id) as number;

    const marketplace = await getMarketplace(dataSources.sqldb.knex, {
      id: marketplaceId,
    }).withGraphJoined('organizations');

    if (!marketplace) {
      return null;
    }

    if (
      authorize(user, ['marketplaces:full', 'marketplaces:list']) ||
      authorize(user, [], {
        scope: RoleScope.MARKETPLACE,
        resourceId: marketplaceId,
      }) ||
      (marketplace.organizations ?? []).some(({ id: resourceId }) =>
        authorize(user, 'organization.marketplaces:list', {
          scope: RoleScope.ORGANIZATION,
          resourceId,
        }),
      )
    ) {
      return marketplace;
    }

    throw new ForbiddenError('Not authorized (marketplace)');
  }
}
