import { ApolloError } from 'apollo-server';
import { find } from 'lodash';
import { SqlDbSource } from '../../datasources';
import { dispatch } from '../dispatch/service';
import { acquireLock } from '../distributed-lock/lock';
import { Appointment, AppointmentResponse } from './sqldb';
import { getAppointment } from './sqldb/queries';
import {
  AppointmentStatus,
  ParticipantType,
  ParticipationStatus,
} from './sqldb/types';

interface DeclineAppointmentParams {
  sqldb: SqlDbSource;
  appointmentId: number;
  profileId: number;
}

export async function declineAppointment(
  params: DeclineAppointmentParams,
): Promise<Appointment | null> {
  const { sqldb, appointmentId, profileId } = params;

  // lock the appointment and profile

  const release = await acquireLock({
    sqldb,
    resources: [`appointments:${appointmentId}`, `profiles:${profileId}`],
  });

  if (!release) {
    throw new ApolloError(
      'The appointment or profile is temporarily locked',
      'decline-appointment:locked',
    );
  }

  try {
    const appointment = await getAppointment(sqldb.knex, { id: appointmentId });

    if (!appointment) {
      throw new ApolloError(
        'Invalid appointment',
        'decline-appointment:appointment',
      );
    }

    // check if appointment is PENDING

    if (appointment.status !== AppointmentStatus.PENDING) {
      throw new ApolloError(
        'Only pending appointments can be declined',
        'decline-appointment:state',
      );
    }

    // check if profile is a participant

    const profile = await sqldb.profile(profileId);

    const participant = find(appointment.participants ?? [], {
      type: ParticipantType.PRACTITIONER,
      profileId,
    });

    if (!profile || !participant) {
      throw new ApolloError(
        'Invalid participant',
        'decline-appointment:participant',
      );
    }

    await sqldb.knex.transaction(async (trx) => {
      // insert appointment response for participant

      await AppointmentResponse.query(trx).insert({
        status: ParticipationStatus.DECLINED,
        participantId: participant.id,
        start: appointment.start,
        end: appointment.end,
      });

      // update participant status (DECLINED)

      await participant
        .$query(trx)
        .patch({ status: ParticipationStatus.DECLINED });

      // update appointment status (CANCELELD)

      await appointment.$query(trx).delete();
    });
  } finally {
    await release();
  }

  const result = await getAppointment(sqldb.knex, { id: appointmentId }).modify(
    'withArchived',
  );

  if (result?.constraintId) {
    const constraint = await result.$relatedQuery('constraint', sqldb.knex);

    if (constraint) {
      dispatch({
        sqldb,
        appointmentRequestId: constraint.requestId,
      });
    }
  }

  return result ?? null;
}
