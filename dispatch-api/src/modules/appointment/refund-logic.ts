import { ApolloError } from 'apollo-server';
import { filter } from 'lodash';
import { PaymentStatus } from '../payment/sqldb/types';
import { AppointmentRefundType } from '../payment/sqldb/types';
import { Checkout, Payment } from '../payment/sqldb';

export interface RefundOptions {
  hasDeposit: boolean;
  depositAmount: number;
  totalPaid: number;
  availableOptions: AppointmentRefundType[];
}

export interface RefundCalculation {
  refundAmount: number;
  description: string;
}

function detectDepositPayment(checkout: Checkout): Payment | null {
  if (!checkout.payments) {
    return null;
  }

  return (
    checkout.payments.find(
      (payment) =>
        payment.isDeposit &&
        payment.status === PaymentStatus.ACCEPTED &&
        payment.amount > 0,
    ) ?? null
  );
}

export function calculateRefundOptions(checkout: Checkout): RefundOptions {
  const acceptedPayments = filter(
    checkout.payments ?? [],
    (payment) =>
      payment.status === PaymentStatus.ACCEPTED && payment.amount > 0,
  );

  const totalPaid = acceptedPayments.reduce(
    (sum: number, payment) => sum + payment.amount,
    0,
  );

  const depositPayment = detectDepositPayment(checkout);
  const hasDeposit = !!depositPayment;
  const depositAmount = depositPayment?.amount ?? 0;

  const availableOptions: AppointmentRefundType[] = [
    AppointmentRefundType.NONE,
    AppointmentRefundType.CUSTOM,
    AppointmentRefundType.FULL,
  ];

  if (hasDeposit) {
    availableOptions.push(AppointmentRefundType.FULL_MINUS_DEPOSIT);
  }

  return {
    hasDeposit,
    depositAmount,
    totalPaid,
    availableOptions,
  };
}

export function calculateRefundAmount(
  refundType: AppointmentRefundType,
  customAmount: number | undefined,
  refundOptions: RefundOptions,
): RefundCalculation {
  const { totalPaid, depositAmount } = refundOptions;

  switch (refundType) {
    case AppointmentRefundType.NONE:
      return {
        refundAmount: 0,
        description: 'No refund',
      };

    case AppointmentRefundType.FULL:
      return {
        refundAmount: totalPaid,
        description: 'Full refund',
      };

    case AppointmentRefundType.FULL_MINUS_DEPOSIT:
      return {
        refundAmount: totalPaid - depositAmount,
        description: 'Full refund minus deposit',
      };

    case AppointmentRefundType.CUSTOM:
      if (customAmount == null) {
        throw new ApolloError(
          'Custom refund amount is required',
          'refund-logic:custom-amount-required',
        );
      }

      if (customAmount < 0) {
        throw new ApolloError(
          'Refund amount cannot be negative',
          'refund-logic:negative-amount',
        );
      }

      if (customAmount > totalPaid) {
        throw new ApolloError(
          'Cannot refund more than the total paid amount',
          'refund-logic:amount-exceeds-paid',
        );
      }

      return {
        refundAmount: customAmount,
        description: 'Custom refund',
      };

    default:
      throw new ApolloError('Invalid refund type', 'refund-logic:invalid-type');
  }
}
