import { ApolloError } from 'apollo-server';
import { SqlDbSource } from '../../datasources';
import { geocodeAddress } from '../common/geocode';
import { dispatch } from '../dispatch/service';
import { createCheckout } from '../payment/create-checkout';
import { PaymentMethod } from '../payment/payment';
import { CheckoutItemType } from '../payment/sqldb/types';
import { appointmentRequestNotification } from '../twilio/notifications';
import { validateAppointmentRequest } from './common';
import { logAppointmentRequest } from './log';
import { AppointmentRequest } from './sqldb';
import {
  AppointmentRequestFields,
  AppointmentRequestStatus,
} from './sqldb/types';
import { trackAppointmentRequest } from '../twilio/analytics';

interface CreateAppointmentRequestParams {
  sqldb: SqlDbSource;
  appointmentRequest: Omit<AppointmentRequestFields, 'status'>;
  paymentMethod?: PaymentMethod;
  gratuity?: number;
  marketplaceUserId?: number;
}

export async function createAppointmentRequest(
  params: CreateAppointmentRequestParams,
): Promise<AppointmentRequest | null> {
  const { sqldb, appointmentRequest, paymentMethod, gratuity } = params;
  const { marketplaceId, location, notes } = appointmentRequest;

  const clientProfileIds = appointmentRequest.clientProfileIds ?? [];
  const procedureBaseDefIds = appointmentRequest.procedureBaseDefIds ?? [];
  const constraints = appointmentRequest.constraints ?? [];

  const marketplace = await sqldb.marketplace(marketplaceId);

  if (!marketplace) {
    throw new ApolloError(
      'Invalid marketplace',
      'appointment-request:marketplace',
    );
  }

  if ((gratuity ?? 0) < 0) {
    throw new ApolloError(
      'Gratuity must be greater than or equal to 0',
      'create-appointment-request:gratuity',
    );
  }

  await validateAppointmentRequest(appointmentRequest, { sqldb });

  const geo = await geocodeAddress(location);

  const status = marketplace.requireDispatchApproval
    ? AppointmentRequestStatus.PENDING_APPROVAL
    : AppointmentRequestStatus.PENDING;

  try {
    const request = await sqldb.knex.transaction(async (trx) =>
      AppointmentRequest.query(trx).insertGraphAndFetch(
        {
          status,
          marketplaceId,
          location,
          latitude: geo?.geometry.location.lat,
          longitude: geo?.geometry.location.lng,
          notes,
          procedureBaseDefs: procedureBaseDefIds.map((id) => ({ id })),
          clientProfiles: clientProfileIds.map((id) => ({ id })),
          constraints: constraints.map((constraint) => ({
            timeRanges: constraint.timeRanges,
            organizations: (constraint.organizationIds ?? []).map((id) => ({
              id,
            })),
            profiles: (constraint.profileIds ?? []).map((id) => ({ id })),
          })),
        },
        {
          relate: [
            'procedureBaseDefs',
            'clientProfiles',
            'constraints.organizations',
            'constraints.profiles',
          ],
        },
      ),
    );

    const marketplaceUserId =
      params.marketplaceUserId ||
      (await sqldb.clientProfile(request.clientProfiles?.[0]?.id))
        ?.marketplaceUser?.id;

    const marketplaceUser = await sqldb.marketplaceUser(marketplaceUserId);

    if (marketplaceUser && marketplaceUser.groupId !== marketplace.groupId) {
      throw new ApolloError(
        'Marketplace user does not match client profile',
        'create-appointment-request:marketplace-user',
      );
    }

    const checkout = await createCheckout({
      sqldb,
      paymentMethod,
      marketplaceUserId,
      marketplaceId,
      items: [
        {
          type: CheckoutItemType.GRATUITY,
          quantity: 1,
          price: gratuity ?? 0,
          description: 'Gratuity',
          key: 'gratuity',
        },
      ],
    });

    await request.$query(sqldb.knex).patchAndFetch({ checkoutId: checkout.id });

    logAppointmentRequest(request, { sqldb });

    if (request.status === AppointmentRequestStatus.PENDING) {
      dispatch({ sqldb, appointmentRequestId: request.id });
    }

    await appointmentRequestNotification({
      sqldb,
      appointmentRequest: request,
      marketplaceUserId,
    });

    trackAppointmentRequest({ request, sqldb });

    return request;
  } catch (err) {
    console.log(err);
    throw new ApolloError(
      'Error creating the appointment request',
      'create-appointment-request:error',
    );
  }
}
