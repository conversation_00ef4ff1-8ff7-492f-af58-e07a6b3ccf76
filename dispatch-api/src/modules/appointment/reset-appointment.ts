import { ApolloError } from 'apollo-server';
import { SqlDbSource } from '../../datasources';
import { acquireLock } from '../distributed-lock/lock';
import { Appointment } from './sqldb';
import { getAppointment } from './sqldb/queries';
import { AppointmentStatus } from './sqldb/types';

interface ResetAppointmentParams {
  sqldb: SqlDbSource;
  appointmentId: number;
}

export async function resetAppointment(
  params: ResetAppointmentParams,
): Promise<Appointment | null> {
  const { sqldb, appointmentId } = params;

  // lock the appointment

  const release = await acquireLock({
    sqldb,
    resources: [`appointments:${appointmentId}`],
  });

  if (!release) {
    throw new ApolloError(
      'The appointment is temporarily locked',
      'reset-appointment:locked',
    );
  }

  try {
    const appointment = await getAppointment(sqldb.knex, { id: appointmentId });

    if (!appointment) {
      throw new ApolloError(
        'Invalid appointment',
        'reset-appointment:appointment',
      );
    }

    if (
      ![AppointmentStatus.BOOKED, AppointmentStatus.COMPLETED].includes(
        appointment.status,
      )
    ) {
      throw new ApolloError(
        'The appointment cannot be reset',
        'reset-appointment:state',
      );
    }

    try {
      await Appointment.query(sqldb.knex).findById(appointmentId).patch({
        status: AppointmentStatus.BOOKED,
        startedAt: null,
        completedAt: null,
      });
    } catch (err) {
      throw new ApolloError(
        'Error updating the appointment',
        'reset-appointment:error',
      );
    }

    return (await getAppointment(sqldb.knex, { id: appointmentId })) ?? null;
  } finally {
    await release();
  }
}
