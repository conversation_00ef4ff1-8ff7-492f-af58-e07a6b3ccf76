import { capitalize } from 'lodash';
import {
  Model,
  Modifiers,
  <PERSON>jo,
  QueryBuilder,
  RelationMappings,
} from 'objection';
import { Field, Float, ID, ObjectType, registerEnumType } from 'type-graphql';
import { ClientProfile } from '../../client-profile/sqldb';
import { ArchivableModel, BaseModel } from '../../common/sqldb';
import { Marketplace } from '../../marketplace/sqldb';
import { Checkout } from '../../payment/sqldb';
import { ProcedureBaseDefinition } from '../../procedure/sqldb';
import AppointmentConstraint from './AppointmentConstraint';
import { AppointmentRequestSortField, AppointmentRequestStatus } from './types';

registerEnumType(AppointmentRequestStatus, {
  name: 'AppointmentRequestStatus',
});

registerEnumType(AppointmentRequestSortField, {
  name: 'AppointmentRequestSortFields',
});

@ObjectType()
export default class AppointmentRequest extends ArchivableModel {
  @Field(() => ID)
  readonly id!: number;
  marketplaceId!: number;

  @Field(() => AppointmentRequestStatus)
  status!: AppointmentRequestStatus;

  @Field()
  location!: string;

  @Field(() => Float, { nullable: true })
  latitude?: number;

  @Field(() => Float, { nullable: true })
  longitude?: number;

  @Field(() => String, { nullable: true })
  notes?: string;

  marketplace?: Marketplace;
  procedureBaseDefs?: ProcedureBaseDefinition[];
  constraints?: AppointmentConstraint[];
  clientProfiles?: ClientProfile[];
  checkout?: Checkout;

  checkoutId?: number;

  static tableName = 'appointmentRequests';

  static relationMappings = (): RelationMappings => ({
    marketplace: {
      relation: Model.BelongsToOneRelation,
      modelClass: require('../../marketplace/sqldb').Marketplace,
      join: {
        from: 'appointmentRequests.marketplaceId',
        to: 'marketplaces.id',
      },
    },
    procedureBaseDefs: {
      relation: Model.ManyToManyRelation,
      modelClass: require('../../procedure/sqldb').ProcedureBaseDefinition,
      modify: 'withArchived',
      join: {
        from: 'appointmentRequests.id',
        through: {
          from: 'appointmentRequestsProcedureBaseDefs.requestId',
          to: 'appointmentRequestsProcedureBaseDefs.baseDefId',
        },
        to: 'procedureBaseDefinitions.id',
      },
    },
    constraints: {
      relation: Model.HasManyRelation,
      modelClass: require('.').AppointmentConstraint,
      join: {
        from: 'appointmentRequests.id',
        to: 'appointmentConstraints.requestId',
      },
    },
    clientProfiles: {
      relation: Model.ManyToManyRelation,
      modelClass: require('../../client-profile/sqldb').ClientProfile,
      join: {
        from: 'appointmentRequests.id',
        through: {
          from: 'appointmentRequestsClientProfiles.requestId',
          to: 'appointmentRequestsClientProfiles.clientProfileId',
        },
        to: 'clientProfiles.id',
      },
    },
    checkout: {
      relation: Model.BelongsToOneRelation,
      modelClass: require('../../payment/sqldb').Checkout,
      join: {
        from: 'appointmentRequests.checkoutId',
        to: 'checkouts.id',
      },
    },
  });

  static modifiers: Modifiers = {
    ...ArchivableModel.modifiers,
    pending(query: QueryBuilder<Model>): void {
      const { ref } = AppointmentRequest;
      query.whereIn(ref('status'), [
        AppointmentRequestStatus.PENDING,
        AppointmentRequestStatus.PENDING_APPROVAL,
      ]);
    },
  };

  $parseDatabaseJson(json: Pojo): Pojo {
    json = super.$parseDatabaseJson(json);
    BaseModel.toNumber(json, 'latitude');
    BaseModel.toNumber(json, 'longitude');
    return json;
  }

  toString(): string {
    const tzid = this.clientProfiles?.[0].tzid;

    const indicator =
      {
        [AppointmentRequestStatus.PENDING_APPROVAL]: '🟠',
        [AppointmentRequestStatus.PENDING]: '🔵',
        [AppointmentRequestStatus.FULFILLED]: '🟢',
        [AppointmentRequestStatus.CANCELLED]: '🔴',
      }[this.status] ?? '⚫';

    return [
      `Appointment Request (id:${this.id})`,
      `Status: ${indicator} ${capitalize(this.status)}`,
      `Procedure: ${(this.procedureBaseDefs ?? [])
        .map((def) => def.name)
        .join(', ')}`,
      `Location: ${this.location}`,
      `${(this.constraints ?? [])
        .map(
          (constraint, i) =>
            `Constraint ${i + 1}:\n${constraint
              .toString(tzid)
              .split('\n')
              .map((line) => `    ${line}`)
              .join('\n')}`,
        )
        .join('\n')}`,
    ].join('\n');
  }
}
