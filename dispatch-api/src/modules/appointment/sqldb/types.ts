import {
  FilterDateFields,
  FilterNumberFields,
  FilterStringFields,
  SortDirection,
} from '../../common/sqldb/types';
import { ProcedureBaseDefinition } from '../../procedure/sqldb';

export enum AppointmentStatus {
  PENDING = 'pending',
  BOOKED = 'booked',
  // ARRIVED = 'arrived',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  NOSHOW = 'noshow',
}

export enum ParticipantType {
  PATIENT = 'patient',
  PRACTITIONER = 'practitioner',
}

export enum ParticipationStatus {
  ACCEPTED = 'accepted',
  DECLINED = 'declined',
  TENTATIVE = 'tentative',
  NEEDS_ACTION = 'needs_action',
}

export enum AppointmentRequestStatus {
  PENDING_APPROVAL = 'pending_approval',
  PENDING = 'pending',
  FULFILLED = 'fulfilled',
  CANCELLED = 'cancelled',
}

export enum AppointmentSortField {
  ID = 'id',
  STATUS = 'status',
  START = 'start',
  PRACTITIONER_NAME = 'practitioner_name',
  PROCEDURE = 'procedure',
  DURATION = 'duration',
  CLIENT_NAME = 'client_name',
  LOCATION = 'location',
  ORDERAPPROVED = 'orderApproved',
  BALANCE = 'balance',
  CREATEDAT = 'createdAt',
  UPDATEDAT = 'updatedAt',
}

export enum AppointmentRequestSortField {
  ID = 'id',
  STATUS = 'status',
  PROCEDURE = 'procedure',
  CLIENT_NAME = 'client_name',
  LOCATION = 'location',
  MARKETPLACE_NAME = 'marketplace_name',
  MEMBERSHIP = 'membership',
  BALANCE = 'balance',
  CREATEDAT = 'createdAt',
  UPDATEDAT = 'updatedAt',
}

export interface AppointmentParticipantFields {
  type: ParticipantType;
  status: ParticipationStatus;
  name: string;
  clientProfileId?: number;
  profileId?: number;
}

export interface AppointmentFields {
  status: AppointmentStatus;
  start: Date;
  end: Date;
  duration: number;
  location: string;
  latitude?: number;
  longitude?: number;
  notes?: string;
  participants?: AppointmentParticipantFields[];
  procedureBaseDefs?: ProcedureBaseDefinition[];
  checkoutId?: number;
}

export interface AppointmentTimeFields {
  start: Date;
}

export interface AppointmentTimeRangeFields {
  start: Date;
  end: Date;
}

export interface AppointmentConstraintFields {
  organizationIds?: number[];
  profileIds?: number[];
  timeRanges: AppointmentTimeRangeFields[];
}

export interface AppointmentRequestFields {
  status: AppointmentRequestStatus;
  marketplaceId: number;
  location: string;
  latitude?: number;
  longitude?: number;
  notes?: string;
  clientProfileIds?: number[];
  procedureBaseDefIds?: number[];
  constraints?: AppointmentConstraintFields[];
}

export interface AppointmentSortFields {
  field: AppointmentSortField;
  direction: SortDirection;
}

export interface AppointmentFilterFields {
  id?: FilterNumberFields;
  status?: FilterStringFields;
  start?: FilterDateFields;
  practitioner_name?: FilterStringFields;
  procedure?: FilterStringFields;
  duration?: FilterNumberFields;
  client_name?: FilterStringFields;
  location?: FilterStringFields;
  orderApproved?: boolean;
}

export interface AppointmentRequestSortFields {
  field: AppointmentRequestSortField;
  direction: SortDirection;
}

export interface AppointmentRequestFilterFields {
  id?: FilterNumberFields;
  status?: FilterStringFields;
  organization_name?: FilterStringFields;
  practitioner_name?: FilterStringFields;
  procedure?: FilterStringFields;
  client_name?: FilterStringFields;
  location?: FilterStringFields;
  marketplace_name?: FilterStringFields;
  marketplace_id?: FilterNumberFields;
  membership?: FilterStringFields;
}
