import { ApolloError } from 'apollo-server';
import { SqlDbSource } from '../../datasources';
import { acquireLock } from '../distributed-lock/lock';
import { Appointment } from './sqldb';
import { getAppointment } from './sqldb/queries';
import { AppointmentStatus } from './sqldb/types';

interface StartAppointmentParams {
  sqldb: SqlDbSource;
  appointmentId: number;
  startedAt: Date;
}

export async function startAppointment(
  params: StartAppointmentParams,
): Promise<Appointment | null> {
  const { sqldb, appointmentId, startedAt } = params;

  // lock the appointment

  const release = await acquireLock({
    sqldb,
    resources: [`appointments:${appointmentId}`],
  });

  if (!release) {
    throw new ApolloError(
      'The appointment is temporarily locked',
      'start-appointment:locked',
    );
  }

  try {
    const appointment = await getAppointment(sqldb.knex, { id: appointmentId });

    if (!appointment) {
      throw new ApolloError(
        'Invalid appointment',
        'start-appointment:appointment',
      );
    }

    if (appointment.status !== AppointmentStatus.BOOKED) {
      throw new ApolloError(
        'The appointment must be in the BOOKED state',
        'start-appointment:state',
      );
    }

    try {
      await Appointment.query(sqldb.knex)
        .findById(appointmentId)
        .patch({ startedAt });
    } catch (err) {
      throw new ApolloError(
        'Error updating the appointment',
        'start-appointment:error',
      );
    }

    return (await getAppointment(sqldb.knex, { id: appointmentId })) ?? null;
  } finally {
    await release();
  }
}
