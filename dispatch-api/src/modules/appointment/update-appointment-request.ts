import { ApolloError } from 'apollo-server';
import { SqlDbSource } from '../../datasources';
import { geocodeAddress } from '../common/geocode';
import { acquireLock } from '../distributed-lock/lock';
import { validateAppointmentRequest } from './common';
import { logAppointmentRequest } from './log';
import { AppointmentRequest } from './sqldb';
import { getAppointmentRequest } from './sqldb/queries';
import {
  AppointmentRequestFields,
  AppointmentRequestStatus,
} from './sqldb/types';

interface UpdateAppointmentRequestParams {
  sqldb: SqlDbSource;
  appointmentRequestId: number;
  updateParams: Partial<
    Pick<
      AppointmentRequestFields,
      | 'location'
      | 'notes'
      | 'clientProfileIds'
      | 'procedureBaseDefIds'
      | 'constraints'
    >
  >;
}

export async function updateAppointmentRequest(
  params: UpdateAppointmentRequestParams,
): Promise<AppointmentRequest | null> {
  const { sqldb, appointmentRequestId, updateParams } = params;

  // lock the appointment request

  const releaseRequest = await acquireLock({
    sqldb,
    resources: [`appointment_requests:${appointmentRequestId}`],
  });

  if (!releaseRequest) {
    throw new ApolloError(
      'The appointment request is temporarily locked',
      'cancel-appointment-request:locked',
    );
  }

  try {
    const appointmentRequest = await getAppointmentRequest(sqldb.knex, {
      id: appointmentRequestId,
    });

    if (!appointmentRequest) {
      throw new ApolloError(
        'Invalid appointment request',
        'update-appointment-request:appointment-request',
      );
    }

    if (
      appointmentRequest.status !== AppointmentRequestStatus.PENDING_APPROVAL
    ) {
      throw new ApolloError(
        'The appointment request must be in the PENDING_APPROVAL state',
        'update-appointment-request:state',
      );
    }

    const appointmentRequestParams: AppointmentRequestFields = {
      status: appointmentRequest.status,
      marketplaceId: appointmentRequest.marketplaceId,
      location: updateParams.location ?? appointmentRequest.location,
      latitude: appointmentRequest.latitude,
      longitude: appointmentRequest.longitude,
      notes: updateParams.notes ?? appointmentRequest.notes,
    };

    const constraints = updateParams.constraints?.map((constraint) => ({
      timeRanges: constraint.timeRanges,
      organizations: (constraint.organizationIds ?? []).map((id) => ({
        id,
      })),
      profiles: (constraint.profileIds ?? []).map((id) => ({ id })),
    }));

    await validateAppointmentRequest(
      {
        ...appointmentRequestParams,
        clientProfileIds: updateParams.clientProfileIds,
        procedureBaseDefIds: updateParams.procedureBaseDefIds,
        constraints,
      },
      { sqldb },
    );

    const appointmentRequestGraph: Record<string, unknown> = {
      ...appointmentRequestParams,
    };

    if (
      appointmentRequestParams.location !== appointmentRequestParams.location ||
      !appointmentRequestParams.latitude ||
      !appointmentRequestParams.longitude
    ) {
      const geo = await geocodeAddress(appointmentRequestParams.location);
      appointmentRequestGraph.latitude = geo?.geometry.location.lat;
      appointmentRequestGraph.longitude = geo?.geometry.location.lng;
    }

    if (updateParams.procedureBaseDefIds) {
      appointmentRequestGraph.procedureBaseDefs =
        updateParams.procedureBaseDefIds?.map((id) => ({
          id,
        }));
    }

    if (updateParams.clientProfileIds) {
      appointmentRequestGraph.clientProfiles =
        updateParams.clientProfileIds?.map((id) => ({
          id,
        }));
    }

    if (updateParams.constraints) {
      appointmentRequestGraph.constraints = constraints;
    }

    try {
      await sqldb.knex.transaction(async (trx) =>
        AppointmentRequest.query(trx)
          .modify('withArchived')
          .upsertGraph(
            {
              id: appointmentRequestId,
              ...appointmentRequestGraph,
            },
            {
              relate: [
                'procedureBaseDefs',
                'clientProfiles',
                'constraints.organizations',
                'constraints.profiles',
              ],
              unrelate: [
                'procedureBaseDefs',
                'clientProfiles',
                'constraints.organizations',
                'constraints.profiles',
              ],
            },
          ),
      );
    } catch (err) {
      console.log(err);
      throw new ApolloError(
        'Error updating the appointment request',
        'update-appointment-request:error',
      );
    }

    const request = await getAppointmentRequest(sqldb.knex, appointmentRequest);

    if (request) {
      logAppointmentRequest(request, { sqldb, isUpdate: true });
    }

    return request ?? null;
  } finally {
    await releaseRequest();
  }
}
