import { ApolloError } from 'apollo-server';
import { SqlDbSource } from '../../datasources';
import { dispatch } from '../dispatch/service';
import { acquireLock } from '../distributed-lock/lock';
import { AppointmentRequest } from './sqldb';
import { getAppointmentRequest } from './sqldb/queries';
import { AppointmentRequestStatus } from './sqldb/types';

interface ApproveAppointmentRequestParams {
  sqldb: SqlDbSource;
  appointmentRequestId: number;
}

export async function approveAppointmentRequest(
  params: ApproveAppointmentRequestParams,
): Promise<AppointmentRequest | null> {
  const { sqldb, appointmentRequestId } = params;

  // lock the appointment request

  const releaseRequest = await acquireLock({
    sqldb,
    resources: [`appointment_requests:${appointmentRequestId}`],
  });

  if (!releaseRequest) {
    throw new ApolloError(
      'The appointment request is temporarily locked',
      'approve-appointment-request:locked',
    );
  }

  try {
    const appointmentRequest = await getAppointmentRequest(sqldb.knex, {
      id: appointmentRequestId,
    });

    if (!appointmentRequest) {
      throw new ApolloError(
        'Invalid appointment',
        'approve-appointment-request:appointment-request',
      );
    }

    if (
      appointmentRequest.status !== AppointmentRequestStatus.PENDING_APPROVAL
    ) {
      throw new ApolloError(
        'The appointment request is not pending approval',
        'approve-appointment-request:state',
      );
    }

    try {
      await appointmentRequest
        .$query(sqldb.knex)
        .patch({ status: AppointmentRequestStatus.PENDING });
    } catch (err) {
      throw new ApolloError(
        'Error approving the appointment request',
        'approve-appointment-request:error',
      );
    }
  } finally {
    releaseRequest();
  }

  dispatch({ sqldb, appointmentRequestId });

  return (
    (await getAppointmentRequest(sqldb.knex, { id: appointmentRequestId })) ??
    null
  );
}
