import { ApolloError } from 'apollo-server';
import { SqlDbSource } from '../../datasources';
import { acquireLock } from '../distributed-lock/lock';
import { Appointment } from './sqldb';
import { getAppointment } from './sqldb/queries';
import { AppointmentStatus } from './sqldb/types';
import { birdeyeCheckIn, reviewCheckIn } from '../reviews/check-in';
import { attentiveAppointmentCompleted } from '../attentive/events';
import { trackAppointment } from '../twilio/analytics';

interface CompleteAppointmentParams {
  sqldb: SqlDbSource;
  appointmentId: number;
  startedAt?: Date;
  completedAt: Date;
}

export async function completeAppointment(
  params: CompleteAppointmentParams,
): Promise<Appointment | null> {
  const { sqldb, appointmentId, completedAt } = params;

  // lock the appointment

  const release = await acquireLock({
    sqldb,
    resources: [`appointments:${appointmentId}`],
  });

  if (!release) {
    throw new ApolloError(
      'The appointment is temporarily locked',
      'start-appointment:locked',
    );
  }

  try {
    const appointment = await getAppointment(sqldb.knex, { id: appointmentId });

    if (!appointment) {
      throw new ApolloError(
        'Invalid appointment',
        'complete-appointment:appointment',
      );
    }

    const startedAt = params.startedAt || appointment.startedAt;

    if (appointment.status !== AppointmentStatus.BOOKED || !startedAt) {
      throw new ApolloError(
        'The appointment must be in the BOOKED state',
        'complete-appointment:state',
      );
    }

    if (completedAt < startedAt) {
      throw new ApolloError(
        'Invalid start/end times',
        'complete-appointment:timestamps',
      );
    }

    try {
      await Appointment.query(sqldb.knex).findById(appointmentId).patch({
        status: AppointmentStatus.COMPLETED,
        startedAt,
        completedAt,
      });

      appointment.status = AppointmentStatus.COMPLETED;
      birdeyeCheckIn(appointment, sqldb);
      reviewCheckIn(appointment, sqldb);
      attentiveAppointmentCompleted(appointment, sqldb);
      trackAppointment({ appointment, sqldb });
    } catch (err) {
      throw new ApolloError(
        'Error updating the appointment',
        'complete-appointment:error',
      );
    }

    return (await getAppointment(sqldb.knex, { id: appointmentId })) ?? null;
  } finally {
    await release();
  }
}
