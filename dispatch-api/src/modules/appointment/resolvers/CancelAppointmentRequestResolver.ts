import { ApolloError } from 'apollo-server';
import { Arg, Ctx, Mutation, Resolver } from 'type-graphql';
import { ResolverContext } from '../../../api/context';
import { CurrentUser, intFromID } from '../../common/type-graphql';
import { User } from '../../user/sqldb';
import { cancelAppointmentRequest } from '../cancel-appointment-request';
import { authorizeAppointmentRequestRecord } from '../common';
import { AppointmentRequest } from '../sqldb';
import CancelAppointmentRequestInput from './CancelAppointmentRequestInput';

@Resolver()
export default class CancelAppointmentRequestResolver {
  @Mutation(() => AppointmentRequest, { nullable: true })
  async cancelAppointmentRequest(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Arg('input') input: CancelAppointmentRequestInput,
  ): Promise<AppointmentRequest | null> {
    const appointmentRequest = await sqldb.appointmentRequest(
      intFromID(input.appointmentRequestId),
    );

    if (!appointmentRequest) {
      throw new ApolloError(
        'Invalid appointment request id',
        'appointment-request:id',
      );
    }

    await authorizeAppointmentRequestRecord(user, appointmentRequest, {
      sqldb,
    });

    return cancelAppointmentRequest({
      sqldb,
      appointmentRequestId: appointmentRequest.id,
    });
  }
}
