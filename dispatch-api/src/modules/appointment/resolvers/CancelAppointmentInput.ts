import { Field, ID, InputType, Int, registerEnumType } from 'type-graphql';
import { IsNumberID } from '../../common/type-graphql';
import { AppointmentRefundType } from '../../payment/sqldb/types';

registerEnumType(AppointmentRefundType, {
  name: 'AppointmentRefundType',
});

@InputType()
export default class CancelAppointmentInput {
  @Field(() => ID)
  @IsNumberID()
  appointmentId!: string;

  @Field({ nullable: true })
  reason?: string;

  @Field(() => AppointmentRefundType, { nullable: true })
  refundType?: AppointmentRefundType;

  @Field(() => Int, { nullable: true })
  customRefundAmount?: number;
}
