import { ApolloError } from 'apollo-server';
import dayjs from 'dayjs';
import jwt from 'jsonwebtoken';
import { capitalize, filter, map, sortBy, uniqBy } from 'lodash';
import fetch from 'node-fetch';
import { getConfig } from '../../config';
import { SqlDbSource } from '../../datasources';
import { ParticipantType } from '../appointment/sqldb/types';
import { qualiphyInviteEvent } from '../attentive/events';
import { StateCode } from '../common/sqldb/types';
import { safeJSONParse } from '../common/util';
import { createOrder } from '../emr/create-order';
import { lockForm } from '../emr/lock-form';
import { ProviderType } from '../emr/sqldb/types';
import SlackLogger from '../logger/SlackLogger';
import { ProcedureBaseDefinition } from '../procedure/sqldb';
import { qualiphyApiOrigin } from './integration';
import { QualiphyInvitation } from './sqldb';
import { QualiphyInvitationStatus } from './sqldb/types';

const getStatus = (status: string) => {
  const s = status.toLowerCase();
  if (s === 'approved') return QualiphyInvitationStatus.APPROVED;
  if (s === 'rejected') return QualiphyInvitationStatus.REJECTED;
  if (s === 'defer to physician') return QualiphyInvitationStatus.DEFERRED;
  return QualiphyInvitationStatus.PENDING;
};

interface ExamParams {
  id: number;
  status: string;
  url: string;
  reason: string;
  clinic: string;
  provider: string;
}

interface UpdateQualiphyInvitationParams {
  sqldb: SqlDbSource;
  invitation: QualiphyInvitation;
  exam: ExamParams;
}

export async function updateQualiphyInvitation(
  params: UpdateQualiphyInvitationParams,
): Promise<QualiphyInvitation> {
  const { sqldb, exam, invitation } = params;
  const { id, status, url, reason, clinic, provider } = exam;

  const exams = invitation.exams?.map((e) => ({
    id: e.id,
    status: e.qualiphyId === id ? getStatus(status) : e.status,
    info:
      e.qualiphyId === id
        ? JSON.stringify({ reason, url, clinic, provider })
        : e.info,
  }));

  return QualiphyInvitation.query(sqldb.knex)
    .withGraphFetched('exams.procedureDefinitions.qualiphyExams')
    .upsertGraphAndFetch(
      { id: invitation.id, exams },
      { relate: ['exams'], unrelate: ['exams'] },
    );
}

export interface QualiphyJwtPayload {
  inviteId: number;
}

interface QualiphyResponseExam {
  patient_exam_id: number;
  exam_title: string;
  exam_id: number;
}

interface InviteRequestBody {
  api_key: string;
  exams: number[];
  first_name: string;
  last_name: string;
  email: string;
  dob: string;
  phone_number: string;
  webhook_url: string;
  additional_data: string;
  tele_state: StateCode;
}

interface SendQualiphyExamInviteParams {
  sqldb: SqlDbSource;
  appointmentId: number;
}

export async function sendExamInviteForAppointment({
  sqldb,
  appointmentId,
}: SendQualiphyExamInviteParams): Promise<QualiphyInvitation> {
  const config = getConfig();

  const appointment = await sqldb.appointment(appointmentId);

  if (!appointment) {
    throw new ApolloError(
      'Invalid appointment id',
      'send-exam-invite-for-appointment:id',
    );
  }

  const client = appointment.participants?.find(
    (p) => p.type === ParticipantType.PATIENT,
  )?.clientProfile;

  if (!client) {
    throw new ApolloError(
      'Client profile not found',
      'send-exam-invite-for-appointment:client',
    );
  }

  const practitioner = appointment.participants?.find(
    (p) => p.type === ParticipantType.PRACTITIONER,
  )?.profile;

  const organizationId = practitioner?.organizationId;
  const organization = await sqldb.organization(organizationId);

  if (!organization) {
    throw new ApolloError(
      'Invalid organization',
      'send-exam-invite-for-appointment:organization',
    );
  }

  if (!organization.state) {
    throw new ApolloError(
      'A state must be configured for the organization to send Qualiphy invitations',
      'send-exam-invite-for-appointment:state',
    );
  }

  const integration = await sqldb.qualiphyIntegrationForOrg(organizationId);

  if (!integration?.enabled || !integration?.encryptedApiKey) {
    throw new ApolloError(
      'Qualiphy is not enabled for this organization',
      'send-exam-invite-for-appointment:disabled',
    );
  }

  const apiKey = integration.encryptedApiKey.decrypt();

  const baseDefs = await ProcedureBaseDefinition.fetchGraph(
    appointment.procedureBaseDefs ?? [],
    'procedureDefs(filterOrganization).qualiphyExams',
    {
      transaction: sqldb.knex,
    },
  ).modifiers({
    filterOrganization(builder) {
      builder.where('organizationId', organization.id);
    },
    filterArchived(builder) {
      builder.where('archived', false);
    },
  });

  const defs = baseDefs.flatMap((b) => b.procedureDefs || []);
  const exams = uniqBy(defs?.flatMap((p) => p?.qualiphyExams || []), 'id');

  if (!exams.length) {
    throw new ApolloError(
      'Procedures on appointment are not mapped to any active Qualiphy exams. Previously mapped exams may have been archived.',
      'send-exam-invite-for-appointment:no-exams',
    );
  }

  let invite = await QualiphyInvitation.query(sqldb.knex).insertAndFetch({
    appointmentId,
    organizationId,
    expiration: dayjs().add(90, 'days').toDate(),
  });

  if (!invite)
    throw new ApolloError(
      'Error creating the invitation',
      'send-exam-invite-for-appointment:invitation',
    );

  const payload: QualiphyJwtPayload = {
    inviteId: invite.id,
  };

  const token = jwt.sign(payload, `${config.auth.secret}`, {
    expiresIn: '90d',
  });

  const inviteBody: InviteRequestBody = {
    api_key: apiKey,
    exams: exams.map((exam) => exam.qualiphyId),
    first_name: client.givenName,
    last_name: client.familyName,
    email: client.email,
    phone_number: client.phone,
    dob: dayjs(client.dob).format('YYYY-MM-DD'),
    webhook_url: `${config.api.origin}/webhooks/qualiphy`,
    additional_data: JSON.stringify({
      token,
    }),
    tele_state: organization.state,
  };

  try {
    const url = `${qualiphyApiOrigin}/api/exam_invite/`;
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
      body: JSON.stringify(inviteBody),
    });

    const data = await response.json();

    if (data.http_code === 200) {
      const patientExams = data.patient_exams ?? [];
      const meetingUrl = data.meeting_url ?? null;
      const meetingUuid = data.meeting_uuid ?? null;

      if (!patientExams.length || !meetingUrl || !meetingUuid) {
        throw new ApolloError(
          'Error sending invitation, data missing from Qualiphy response',
          'send-qualiphy-exam-invite:data',
        );
      }

      invite = await QualiphyInvitation.query(sqldb.knex).upsertGraphAndFetch(
        {
          ...invite,
          meetingUrl,
          meetingUuid,
          exams: exams.map(({ id, title }) => ({
            id,
            status: QualiphyInvitationStatus.PENDING,
            patientExamId: patientExams?.find(
              (e: QualiphyResponseExam) => e.exam_title === title,
            )?.patient_exam_id,
          })),
        },
        { relate: ['exams'] },
      );

      new SlackLogger({
        sqldb,
        organizationId,
      }).send({
        header: `Qualiphy exam invitation sent (id:${invite.id})`,
        content: invite.toString(),
      });

      qualiphyInviteEvent({
        meetingUrl: data.meeting_url,
        baseDefs,
        sqldb,
        client,
      });
      return invite;
    }

    throw new ApolloError(
      `Error sending invitation to Qualiphy: ${data.error_message}`,
      'send-qualiphy-exam-invite:invitation',
    );
  } catch (err) {
    console.log(err);
    throw err;
  }
}

interface OnUpdatedParams {
  sqldb: SqlDbSource;
  invite: QualiphyInvitation;
  formId: number;
  patientId?: number;
}

export async function onUpdated({
  sqldb,
  invite,
  formId,
  patientId,
}: OnUpdatedParams) {
  if (!patientId) {
    return;
  }

  const appointment = await sqldb.appointment(invite.appointmentId);
  const exams = invite.exams;

  const completeExams = exams?.filter(
    (e) => e.status !== QualiphyInvitationStatus.PENDING,
  );

  if (exams && exams?.length === completeExams?.length) {
    const approvedExams = completeExams?.filter(
      (e) => e.status === QualiphyInvitationStatus.APPROVED,
    );

    for (const exam of approvedExams ?? []) {
      const procedureDefs = (exam.procedureDefinitions || []).filter((p) => {
        const sortedExams = sortBy(
          filter(p.qualiphyExams, (e) =>
            approvedExams.some((exam) => exam.id === e.id),
          ),
          'index',
        );

        let examIndex = -1;
        for (let i = 0; i < sortedExams.length; i++) {
          if (sortedExams[i].id === exam.id) {
            examIndex = i;
            break;
          }
        }

        return examIndex === sortedExams.length - 1;
      });

      const info: Record<string, string> | null =
        safeJSONParse(exam.info) ?? {};
      const notes = ['Approved by Qualiphy'];

      ['reason', 'clinic', 'provider'].forEach((key) => {
        if (info[key]) {
          notes.push(`${capitalize(key)}: ${info[key]}`);
        }
      });

      const note = notes.join('\n');

      const now = new Date();
      let startsAt = appointment?.start;
      if (!startsAt || startsAt > now) {
        startsAt = now;
      }

      const expiresAt = dayjs(startsAt).add(exam.expiresAfter, 'days').toDate();

      await createOrder({
        sqldb,
        patientId,
        providerType: ProviderType.QUALIPHY,
        procedureDefIds: map(procedureDefs, 'id'),
        order: {
          startsAt,
          expiresAt,
          refills: exam.refills,
          note,
        },
      });
    }

    await lockForm({ sqldb, formId, providerType: ProviderType.QUALIPHY });
  }
}

interface ResendQualiphyExamInviteParams {
  sqldb: SqlDbSource;
  inviteId: number;
}

export async function resendExamInvite({
  sqldb,
  inviteId,
}: ResendQualiphyExamInviteParams): Promise<QualiphyInvitation> {
  const invite = await sqldb.qualiphyInvitation(inviteId);

  if (!invite) {
    throw new ApolloError(
      'Invalid invite id',
      'resend-qualiphy-exam-invite:id',
    );
  }

  const appointment = invite.appointment;

  if (!appointment) {
    throw new ApolloError(
      'Appointment not found',
      'resend-qualiphy-exam-invite:invitation',
    );
  }

  const organizationId = invite.organizationId;

  const integration = await sqldb.qualiphyIntegrationForOrg(organizationId);

  if (!integration || !integration?.enabled || !integration?.encryptedApiKey) {
    throw new ApolloError(
      'Qualiphy is not properly configured for this organization',
      'resend-qualiphy-exam-invite:integration',
    );
  }

  const apiKey = integration.encryptedApiKey.decrypt();

  const exams = invite.exams?.filter(
    (e) => e.status === QualiphyInvitationStatus.PENDING,
  );

  if (!exams?.length) {
    throw new ApolloError(
      'No pending exams found for invitation',
      'resend-exam-invite-for-appointment:exams',
    );
  }

  const url = `${qualiphyApiOrigin}/api/exam_invite_resend/`;

  // logging for visibility
  console.log(exams);

  const meetingUuid = invite.meetingUuid;
  const patientExamId = exams.find((e) => Boolean(e.patientExamId))
    ?.patientExamId;

  // separate checks for higher visibility
  if (!meetingUuid) {
    throw new ApolloError(
      'Meeting UUID not found. Cannot resend invitations sent before v2 update.',
      'resend-qualiphy-exam-invite:invitation',
    );
  }

  if (!patientExamId) {
    throw new ApolloError(
      'Patient exam id not found. Cannot resend invitations sent before v2 update.',
      'resend-qualiphy-exam-invite:invitation',
    );
  }

  try {
    const body = {
      api_key: apiKey,
      meeting_uuid: meetingUuid,
      patient_exam_id: patientExamId,
    };

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
      body: JSON.stringify(body),
    });

    const data = await response.json();

    if (data.http_code !== 200) {
      throw new ApolloError(
        `Error resending Qualiphy exam invitation: ${
          data.error_message || 'Error not specified'
        }`,
        'resend-qualiphy-exam-invite:invitation',
      );
    }
  } catch (err) {
    console.log(err);
    throw err;
  }

  new SlackLogger({
    sqldb,
    organizationId,
  }).send({
    header: `Qualiphy exam invitation resent (id:${invite.id})`,
    content: invite.toString(),
  });

  return invite;
}
