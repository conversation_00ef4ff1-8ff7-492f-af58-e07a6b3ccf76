import { Analytics } from '@segment/analytics-node';
import { ClientProfile } from '../client-profile/sqldb';
import { AppointmentRequest, Appointment } from '../appointment/sqldb';
import { SqlDbSource } from '../../datasources';
import {
  clientProfileFromAppointment,
  organizationFromAppointment,
  marketplaceFromAppointment,
  practitionerFromAppointment,
} from '../appointment/common';
import { AppointmentStatus } from '../appointment/sqldb/types';
import { calculateAge } from '../common/util';

function getEvent(status: AppointmentStatus) {
  let event = 'Appointment Booked';
  if (status === AppointmentStatus.CANCELLED) {
    event = 'Appointment Cancelled';
  } else if (status === AppointmentStatus.COMPLETED) {
    event = 'Appointment Completed';
  }
  return event;
}

interface GetAnalyticsParams {
  marketplaceId?: number;
  sqldb: SqlDbSource;
}

async function getAnalytics({ marketplaceId, sqldb }: GetAnalyticsParams) {
  const integration =
    await sqldb.segmentIntegrationForMarketplace(marketplaceId);

  if (!integration || !integration.enabled || !integration.encryptedWriteKey) {
    return null;
  }

  return new Analytics({ writeKey: integration.encryptedWriteKey.decrypt() });
}

interface IdentifyClientParams {
  profile: ClientProfile;
  marketplaceId?: number;
  sqldb: SqlDbSource;
}

async function identifyClient({
  profile,
  marketplaceId,
  sqldb,
}: IdentifyClientParams) {
  const { email, familyName, givenName, createdAt, phone, address, id } =
    profile;

  const age = calculateAge(profile.dob);

  const analytics = await getAnalytics({ marketplaceId, sqldb });

  const traits = {
    email: email,
    name: `${givenName} ${familyName}`,
    firstName: givenName,
    lastName: familyName,
    phone,
    clientProfielId: id,
    fullAddress: address,
    age,
    birthday: new Date(profile.dob),
    createdAt,
  };

  analytics?.identify({
    userId: email,
    traits,
  });
}

interface TrackAppointmentRequestParams {
  request: AppointmentRequest;
  sqldb: SqlDbSource;
}

export async function trackAppointmentRequest({
  request,
  sqldb,
}: TrackAppointmentRequestParams) {
  const {
    id,
    clientProfiles,
    location,
    createdAt,
    status,
    constraints,
    marketplaceId,
    procedureBaseDefs,
    marketplace,
  } = request;

  const [baseDefs, constraintsResult, clients, mp, analytics] =
    await Promise.all([
      procedureBaseDefs ??
        request.$relatedQuery('procedureBaseDefs', sqldb.knex),
      constraints ?? request.$relatedQuery('constraints', sqldb.knex),
      clientProfiles ?? request.$relatedQuery('clientProfiles', sqldb.knex),
      marketplace ?? sqldb.marketplace(marketplaceId),
      getAnalytics({ marketplaceId, sqldb }),
    ]);

  const constraint = constraintsResult?.[0];
  const timeRange = constraint?.timeRanges?.[0];

  clients.forEach((client) => {
    identifyClient({ profile: client, marketplaceId, sqldb });
    const properties = {
      appointmentRequestId: id,
      marketplaceId,
      marketplace: mp?.name,
      address: location,
      status,
      timeRange: timeRange,
      procedures: baseDefs.map((baseDef) => baseDef.name),
      patient: `${client.givenName} ${client.familyName}`,
      createdAt,
    };

    analytics?.track({
      userId: client.email,
      event: 'Appointment Requested',
      properties,
    });
  });
}

interface TrackAppointmentParams {
  appointment: Appointment;
  sqldb: SqlDbSource;
}

export async function trackAppointment({
  appointment,
  sqldb,
}: TrackAppointmentParams) {
  const {
    id,
    location,
    createdAt,
    status,
    start,
    end,
    procedureBaseDefs,
    checkoutId,
  } = appointment;

  const [clientProfile, organization, marketplace, practitioner] =
    await Promise.all([
      clientProfileFromAppointment({
        appointment,
        sqldb,
      }),
      organizationFromAppointment({
        appointment,
        sqldb,
      }),
      marketplaceFromAppointment({ appointment, sqldb }),
      practitionerFromAppointment({
        appointment,
        sqldb,
      }),
    ]);

  const checkout = appointment.checkout ?? (await sqldb.checkout(checkoutId));

  if (!clientProfile || !organization || !marketplace || !practitioner) {
    return;
  }

  if (status === AppointmentStatus.BOOKED) {
    identifyClient({
      profile: clientProfile,
      marketplaceId: marketplace.id,
      sqldb,
    });
  }

  const analytics = await getAnalytics({
    marketplaceId: marketplace.id,
    sqldb,
  });

  const properties = {
    appointmentId: id,
    address: location,
    marketplaceId: marketplace.id,
    marketplace: marketplace.name,
    organizationId: organization.id,
    organization: organization.name,
    patient: `${clientProfile.givenName} ${clientProfile.familyName}`,
    practitioner: `${practitioner.givenName} ${practitioner.familyName}`,
    start,
    end,
    procedures: procedureBaseDefs?.map((baseDef) => ({
      id: baseDef.id,
      name: baseDef.name,
    })),
    status,
    revenue: checkout?.total ? checkout.total / 100 : 0,
    checkout: checkout?.items?.map((item) => ({
      id: item.id,
      description: item.description,
      price: item.price / 100,
      quantity: item.quantity,
    })),
    createdAt,
    cancelledAt:
      status === AppointmentStatus.CANCELLED ? new Date() : undefined,
    completedAt:
      status === AppointmentStatus.COMPLETED ? new Date() : undefined,
  };

  analytics?.track({
    userId: clientProfile.email,
    event: getEvent(status),
    properties,
  });
}

export async function trackAppointmentUpdated({
  appointment,
  sqldb,
}: TrackAppointmentParams) {
  const {
    id,
    location,
    createdAt,
    status,
    start,
    end,
    procedureBaseDefs,
    checkoutId,
  } = appointment;

  const [
    clientProfile,
    organization,
    marketplace,
    practitioner,
    checkoutResult,
  ] = await Promise.all([
    clientProfileFromAppointment({
      appointment,
      sqldb,
    }),
    organizationFromAppointment({
      appointment,
      sqldb,
    }),
    marketplaceFromAppointment({ appointment, sqldb }),
    practitionerFromAppointment({
      appointment,
      sqldb,
    }),
    appointment.checkout ?? sqldb.checkout(checkoutId),
  ]);

  const checkout = checkoutResult;

  if (!clientProfile || !organization || !marketplace || !practitioner) {
    return;
  }

  const analytics = await getAnalytics({
    marketplaceId: marketplace.id,
    sqldb,
  });

  const properties = {
    appointmentId: id,
    address: location,
    marketplaceId: marketplace.id,
    marketplace: marketplace.name,
    organizationId: organization.id,
    organization: organization.name,
    patient: `${clientProfile.givenName} ${clientProfile.familyName}`,
    practitioner: `${practitioner.givenName} ${practitioner.familyName}`,
    start,
    end,
    procedures: procedureBaseDefs?.map((baseDef) => ({
      id: baseDef.id,
      name: baseDef.name,
    })),
    status,
    revenue: checkout?.total,
    checkout: checkout?.items?.map((item) => ({
      id: item.id,
      description: item.description,
      price: item.price,
      quantity: item.quantity,
    })),
    createdAt,
    updatedAt: new Date(),
  };

  analytics?.track({
    userId: clientProfile.email,
    event: 'Appointment Updated',
    properties,
  });
}
