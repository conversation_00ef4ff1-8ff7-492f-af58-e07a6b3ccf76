import { Marketplace } from '../marketplace/sqldb';

interface CalculatePaymentAmountParams {
  marketplace: Marketplace;
  totalAmount: number;
  isAdminPayment?: boolean;
}

interface PaymentAmountResult {
  amountToCharge: number;
  isDeposit: boolean;
  shouldSkipPayment: boolean;
}

export function calculatePaymentAmount(
  params: CalculatePaymentAmountParams,
): PaymentAmountResult {
  const { marketplace, totalAmount, isAdminPayment } = params;

  // When payment is initiated from admin UI, always charge the full amount
  if (isAdminPayment) {
    return {
      amountToCharge: totalAmount,
      isDeposit: false,
      shouldSkipPayment: false,
    };
  }

  const paymentCollectionMethod =
    marketplace.paymentCollectionMethod || 'collect_on_confirmation';

  switch (paymentCollectionMethod) {
    case 'collect_on_site':
      return {
        amountToCharge: 0,
        isDeposit: false,
        shouldSkipPayment: true,
      };

    case 'collect_deposit': {
      if (!marketplace.paymentDepositType || !marketplace.paymentDepositValue) {
        // Fallback to full payment if deposit is not properly configured
        return {
          amountToCharge: totalAmount,
          isDeposit: false,
          shouldSkipPayment: false,
        };
      }

      let depositAmount: number;
      if (marketplace.paymentDepositType === 'percentage') {
        depositAmount = Math.round(
          (totalAmount * marketplace.paymentDepositValue) / 100,
        );
      } else if (marketplace.paymentDepositType === 'fixed_amount') {
        depositAmount = Math.round(marketplace.paymentDepositValue * 100);
      } else {
        // Invalid deposit type, fallback to full payment
        return {
          amountToCharge: totalAmount,
          isDeposit: false,
          shouldSkipPayment: false,
        };
      }

      // Don't charge more than the total amount
      depositAmount = Math.min(depositAmount, totalAmount);

      return {
        amountToCharge: depositAmount,
        isDeposit: true,
        shouldSkipPayment: false,
      };
    }

    case 'collect_on_confirmation':
    default:
      return {
        amountToCharge: totalAmount,
        isDeposit: false,
        shouldSkipPayment: false,
      };
  }
}
