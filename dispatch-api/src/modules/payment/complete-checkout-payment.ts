import { ApolloError } from 'apollo-server';
import { SqlDbSource } from '../../datasources';
import { acquireLock } from '../distributed-lock/lock';
import {
  calculateFee,
  getCheckoutBalance,
  refreshCheckoutTotals,
} from './common';
import { createPayment } from './finix/payment';
import { upsertPaymentInstrument } from './finix/payment-instrument';
import { onPaymentAccepted } from './on-payment-accepted';
import { PaymentFeeProfile, PaymentMethod } from './payment';
import { refundCheckout } from './refund-checkout';
import { Checkout } from './sqldb';
import { PaymentStatus, PaymentType } from './sqldb/types';
import { calculatePaymentAmount } from './calculate-payment-amount';
import { Marketplace } from '../marketplace/sqldb';

interface CompleteCheckoutParams {
  sqldb: SqlDbSource;
  checkoutId: number;
  paymentAccountId: number;
  paymentMethod?: PaymentMethod;
  expectedAmount?: number;
  feeProfile?: PaymentFeeProfile;
  tags?: { [key: string]: string };
  marketplace?: Marketplace;
  isAdminPayment?: boolean;
}

export async function completeCheckoutPayment(
  params: CompleteCheckoutParams,
): Promise<Checkout> {
  const {
    sqldb,
    checkoutId,
    paymentAccountId,
    paymentMethod,
    expectedAmount,
    feeProfile,
    tags,
    marketplace,
    isAdminPayment,
  } = params;

  const release = await acquireLock({
    sqldb,
    resources: [`checkout:${checkoutId}`],
  });

  if (!release) {
    throw new ApolloError(
      'The checkout is temporarily locked',
      'complete-checkout-payment:locked',
    );
  }

  try {
    sqldb.invalidate('checkout', [checkoutId]);
    const checkout = await sqldb.checkout(checkoutId);

    if (!checkout) {
      throw new ApolloError('Invalid checkout', 'complete-checkout:checkout');
    }

    if (checkout.voidedAt) {
      throw new ApolloError(
        'Checkout has been voided',
        'complete-checkout-payment:voided',
      );
    }

    const balance = await getCheckoutBalance({
      sqldb,
      checkout,
    });

    if (expectedAmount != null && expectedAmount !== balance) {
      throw new ApolloError(
        'Checkout balance does not match the expected amount',
        'complete-checkout-payment:expected-amount',
      );
    }

    if (Math.abs(balance) < 1) {
      return checkout;
    }

    if (balance > 0) {
      // Calculate payment amount based on marketplace settings
      let amountToCharge = balance;
      let isDeposit = false;
      let shouldSkipPayment = false;

      if (marketplace) {
        const paymentCalculation = calculatePaymentAmount({
          marketplace,
          totalAmount: balance,
          isAdminPayment,
        });

        amountToCharge = paymentCalculation.amountToCharge;
        isDeposit = paymentCalculation.isDeposit;
        shouldSkipPayment = paymentCalculation.shouldSkipPayment;
      }

      // Skip payment processing if configured to collect onsite
      if (shouldSkipPayment) {
        return checkout;
      }

      let { paymentInstrument } = checkout;

      if (paymentMethod) {
        paymentInstrument = await upsertPaymentInstrument({
          sqldb,
          paymentMethod,
        });
      }

      if (!paymentInstrument) {
        throw new ApolloError(
          'Invalid payment instrument',
          'complete-checkout-payment:payment-instrument',
        );
      }

      const fee = feeProfile
        ? calculateFee({ amount: amountToCharge, feeProfile })
        : 0;

      const payment = await createPayment({
        sqldb,
        checkoutId,
        paymentAccountId,
        type: PaymentType.CREDIT,
        amount: amountToCharge,
        paymentMethod,
        fee,
        isDeposit,
        tags: {
          ...tags,
          checkoutId: String(checkoutId),
          ...(isDeposit && { paymentType: 'deposit' }),
        },
      });

      await refreshCheckoutTotals({ sqldb, checkoutId });
      sqldb.invalidate('checkout', [checkoutId]);

      if (payment.status === PaymentStatus.FAILED) {
        throw new ApolloError(
          (payment.description ?? 'Payment failed') as string,
          'complete-checkout-payment:payment-failed',
        );
      }

      if (payment.status === PaymentStatus.ACCEPTED) {
        await onPaymentAccepted({ sqldb, payment });
      }

      return (await sqldb.checkout(checkoutId)) ?? checkout;
    } else {
      const result = await refundCheckout({
        sqldb,
        checkoutId,
        refundAmount: -balance,
        balanceAfterRefund: 0,
      });

      if ((await getCheckoutBalance({ sqldb, checkout: result })) !== 0) {
        throw new ApolloError(
          'complete-checkout-payment:balance',
          'The checkout balance was not fully refunded',
        );
      }

      return result;
    }
  } finally {
    await release();
  }
}
