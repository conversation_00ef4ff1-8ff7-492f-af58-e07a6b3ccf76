import { SectionBlock } from '@slack/types';
import {
  IncomingWebhookResult,
  IncomingWebhookSendArguments,
} from '@slack/webhook';
import { SqlDbSource } from '../../datasources';
import { logSlack } from './slack';

interface SlackLoggerOptions {
  sqldb: SqlDbSource;
  marketplaceId?: number;
  organizationId?: number;
}

interface SlackLoggerSendOptions {
  content: string;
  header?: string;
}

export default class SlackLogger {
  readonly options?: SlackLoggerOptions;
  webhookUrl?: string;

  constructor(options?: SlackLoggerOptions) {
    if (options) {
      this.options = { ...options };
    }
  }

  async send(
    message: SlackLoggerSendOptions,
  ): Promise<IncomingWebhookResult | null> {
    const sendArgs = buildMessage(message);

    if (this.options) {
      if (!this.webhookUrl) {
        const { sqldb, marketplaceId, organizationId } = this.options;

        if (marketplaceId) {
          const marketplace = await sqldb.marketplace(marketplaceId);
          this.webhookUrl = marketplace?.slackWebhookUrl;
        } else if (organizationId) {
          const organization = await sqldb.organization(organizationId);
          this.webhookUrl = organization?.slackWebhookUrl;
        } else {
          return null;
        }
      }

      return this.webhookUrl
        ? logSlack(sendArgs, { webhookUrl: this.webhookUrl })
        : null;
    }

    return logSlack(sendArgs);
  }
}

const buildMessage = ({
  content,
  header,
}: SlackLoggerSendOptions): IncomingWebhookSendArguments => {
  const blocks: IncomingWebhookSendArguments['blocks'] = [];

  if (header) {
    blocks.push({
      type: 'header',
      text: {
        type: 'plain_text',
        text: header,
      },
    });
  }

  blocks.push({
    type: 'section',
    text: {
      type: 'mrkdwn',
      text: content,
    },
  });

  const text = blocks
    .filter((block) => (block as SectionBlock).text)
    .slice(0, 2)
    .map((block) => (block as SectionBlock).text?.text ?? '')
    .join('\n')
    .split('\n')
    .slice(0, 3)
    .join('\n');

  return { blocks, text };
};
