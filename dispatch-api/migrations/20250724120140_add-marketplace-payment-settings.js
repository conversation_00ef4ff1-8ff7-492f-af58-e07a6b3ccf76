/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema.table('marketplaces', (table) => {
    table
      .string('payment_collection_method')
      .defaultTo('collect_on_confirmation');
    table.string('payment_deposit_type').nullable();
    table.decimal('payment_deposit_value', 10, 2).nullable();
    table.boolean('has_payment_policy').defaultTo(false);
    table.string('payment_policy_name').nullable();
    table.text('payment_policy_text').nullable();
    table.boolean('require_payment_policy_attestation').defaultTo(false);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.table('marketplaces', (table) => {
    table.dropColumn('payment_collection_method');
    table.dropColumn('payment_deposit_type');
    table.dropColumn('payment_deposit_value');
    table.dropColumn('has_payment_policy');
    table.dropColumn('payment_policy_name');
    table.dropColumn('payment_policy_text');
    table.dropColumn('require_payment_policy_attestation');
  });
};
