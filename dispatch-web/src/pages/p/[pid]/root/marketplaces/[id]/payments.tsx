import BreadcrumbsHeader from '@/components/BreadcrumbsHeader';
import CardDataRow from '@/components/CardDataRow';
import Content from '@/components/Content';
import CreatePaymentAccountDialog from '@/components/CreatePaymentAccountDialog';
import FeeProfileCard from '@/components/FeeProfileCard';
import MuiLink from '@/components/Link';
import MarketplaceSettingsTabs from '@/components/MarketplaceSettingsTabs';
import PaymentAccountCard from '@/components/PaymentAccountCard';
import UserFrame from '@/components/UserFrame';
import { RoleScope, useMarketplaceQuery } from '@/generated/graphql';
import { useAuthorize } from '@/hooks/authorize';
import { useProfile } from '@/hooks/profile';
import { usePathHash } from '@/hooks/router';
import { useLayoutStyles } from '@/hooks/styles';
import {
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  Typography,
} from '@material-ui/core';
import AddIcon from '@material-ui/icons/Add';
import EditIcon from '@material-ui/icons/Edit';
import Link from 'next/link';
import { useRouter } from 'next/router';

const listPermission = [
  'organizations:full',
  'organizations:list',
  {
    scope: RoleScope.Organization,
    permission: [],
  },
];

const paymentsPermission = [
  'payments:full',
  {
    scope: RoleScope.Marketplace,
    permission: ['marketplace.payments:full'],
  },
];

const feeProfilePermission = ['payments:full'];

export default function MarketplacePayments(): JSX.Element {
  const layoutClasses = useLayoutStyles();
  const hash = usePathHash();
  const router = useRouter();

  const [canList] = useAuthorize(listPermission);
  const [canEditPayments] = useAuthorize(paymentsPermission);
  const [canEditFeeProfile] = useAuthorize(feeProfilePermission);

  const { profile } = useProfile();
  const id = router.query.id as string;

  const { data: marketplaceData } = useMarketplaceQuery({
    variables: { id },
    skip: !id || !canList,
  });

  const marketplace = marketplaceData?.marketplace;

  const basePath = `/p/${profile?.pid}/root/marketplaces/${marketplace?.id}`;
  const path = `${basePath}/payments`;
  const createDialogOpen = Object.keys(hash).includes('create');

  const getPaymentCollectionMethodLabel = (method?: string | null) => {
    switch (method) {
      case 'collect_on_confirmation':
        return 'Collect payment once appointment is confirmed';
      case 'collect_on_site':
        return 'Collect payment on-site';
      case 'collect_deposit':
        return 'Collect a deposit once appointment is confirmed';
      default:
        return 'Collect payment once appointment is confirmed';
    }
  };

  const getDepositTypeLabel = (type?: string | null) => {
    switch (type) {
      case 'fixed_amount':
        return 'Fixed Amount';
      case 'percentage':
        return 'Percentage of Total';
      default:
        return 'Not specified';
    }
  };

  const getDepositValueLabel = (
    value?: number | null,
    type?: string | null,
  ) => {
    if (value !== undefined && value !== null) {
      return type !== 'percentage' ? `$${value.toFixed(2)}` : `${value}%`;
    }
    return 'Not specified';
  };

  return (
    <UserFrame>
      <Content
        title={`Settings - ${marketplace?.name ?? 'Marketplace'}`}
        maxWidth="md"
      >
        <BreadcrumbsHeader backHref={`/p/${profile?.pid}/root/marketplaces`}>
          <MuiLink
            color="inherit"
            href={`/p/${profile?.pid}/root/marketplaces`}
          >
            Marketplaces
          </MuiLink>
          <Typography color="textPrimary">{marketplace?.name}</Typography>
        </BreadcrumbsHeader>

        <MarketplaceSettingsTabs selected="payments" path={basePath} />

        {canEditPayments && (
          <Card className={layoutClasses.card} variant="outlined">
            <CardHeader title="Payment Settings" />
            <CardContent>
              <CardDataRow
                label="Payment Collection Method"
                value={getPaymentCollectionMethodLabel(
                  marketplace?.paymentCollectionMethod,
                )}
              />
              {marketplace?.paymentCollectionMethod === 'collect_deposit' && (
                <>
                  <CardDataRow
                    label="Deposit Type"
                    value={getDepositTypeLabel(marketplace?.paymentDepositType)}
                  />
                  <CardDataRow
                    label="Deposit Value"
                    value={getDepositValueLabel(
                      marketplace?.paymentDepositValue,
                      marketplace?.paymentDepositType,
                    )}
                  />
                </>
              )}
              <CardDataRow
                label="Payment Policy"
                value={marketplace?.hasPaymentPolicy ? 'Yes' : 'No'}
              />
              {marketplace?.hasPaymentPolicy && (
                <>
                  <CardDataRow
                    label="Policy Name"
                    value={marketplace?.paymentPolicyName || 'Not specified'}
                  />
                  <CardDataRow
                    label="Require User Attestation"
                    value={
                      marketplace?.requirePaymentPolicyAttestation
                        ? 'Yes'
                        : 'No'
                    }
                  />
                </>
              )}
              <Box mt={3} display="flex" flexDirection="row">
                <Link href={`${basePath}/payments/settings`} passHref>
                  <Button
                    component="a"
                    variant="outlined"
                    color="primary"
                    startIcon={<EditIcon />}
                  >
                    Edit payment settings
                  </Button>
                </Link>
              </Box>
            </CardContent>
          </Card>
        )}

        {canEditPayments && (
          <>
            <FeeProfileCard
              marketplace={marketplace}
              canEdit={canEditFeeProfile}
            />

            {marketplace?.paymentAccounts.map((account) => (
              <PaymentAccountCard
                key={account.id}
                marketplace={marketplace}
                paymentAccount={account}
              />
            ))}

            <Box mt={1} display="flex" style={{ gap: 8 }}>
              <Link href={`${path}#create`} passHref>
                <Button
                  component="a"
                  variant="outlined"
                  startIcon={<AddIcon />}
                >
                  Add account
                </Button>
              </Link>
            </Box>
          </>
        )}

        {marketplace && (
          <CreatePaymentAccountDialog
            key={`CreatePaymentAccountDialog-${createDialogOpen}`}
            marketplace={marketplace}
            open={createDialogOpen}
            onClose={() => {
              if (createDialogOpen) {
                router.replace(path);
                router.back();
              }
            }}
            allowExisting
          />
        )}
      </Content>
    </UserFrame>
  );
}
