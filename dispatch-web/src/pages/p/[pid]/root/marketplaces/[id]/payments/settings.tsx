import BreadcrumbsHeader from '@/components/BreadcrumbsHeader';
import Content from '@/components/Content';
import MuiLink from '@/components/Link';
import PaymentSettingsFormFields, {
  PaymentSettingsFields,
  PaymentSettingsSchema,
} from '@/components/PaymentSettingsFormFields';
import UserFrame from '@/components/UserFrame';
import {
  UpdateMarketplaceInput,
  useMarketplaceQuery,
  useUpdateMarketplaceMutation,
} from '@/generated/graphql';
import { useProfile } from '@/hooks/profile';
import { useLayoutStyles } from '@/hooks/styles';
import { formatGraphQLErrors } from '@/utils/apollo-errors';
import {
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  LinearProgress,
  Typography,
} from '@material-ui/core';
import { Alert } from '@material-ui/lab';
import { Form, Formik } from 'formik';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useState } from 'react';

export default function PaymentSettings(): JSX.Element {
  const layoutClasses = useLayoutStyles();
  const router = useRouter();

  const [error, setError] = useState<string[]>([]);

  const [updateMarketplace, { loading: submitting }] =
    useUpdateMarketplaceMutation();

  const { query } = useRouter();
  const id = query.id as string;

  const { profile } = useProfile();
  const { data: marketplaceData, loading } = useMarketplaceQuery({
    variables: { id },
    skip: !id,
  });

  const marketplace = marketplaceData?.marketplace;
  const returnUri = `/p/${profile?.pid}/root/marketplaces/${id}/payments`;

  const initialValues: PaymentSettingsFields = {
    paymentCollectionMethod:
      marketplace?.paymentCollectionMethod ?? 'collect_on_confirmation',
    paymentDepositType: marketplace?.paymentDepositType ?? 'fixed_amount',
    paymentDepositValue: marketplace?.paymentDepositValue ?? 0,
    hasPaymentPolicy: marketplace?.hasPaymentPolicy ?? false,
    paymentPolicyName: marketplace?.paymentPolicyName ?? '',
    paymentPolicyText: marketplace?.paymentPolicyText ?? '',
    requirePaymentPolicyAttestation:
      marketplace?.requirePaymentPolicyAttestation ?? false,
  };

  async function handleSubmit(values: PaymentSettingsFields) {
    if (!profile || !marketplace || submitting) {
      return;
    }

    const input = {
      ...Object.fromEntries(
        Object.entries(values).filter(
          ([key, value]) =>
            initialValues[key as keyof PaymentSettingsFields] !== value,
        ),
      ),
      id: marketplace.id,
    } as UpdateMarketplaceInput;

    try {
      const { errors } = await updateMarketplace({
        variables: { input },
      });

      if (errors?.length) {
        setError(
          formatGraphQLErrors(errors) ?? ['Error updating payment settings'],
        );
      } else {
        router.push(returnUri);
        return;
      }
    } catch (err) {
      setError(formatGraphQLErrors(err.graphQLErrors) ?? [err.message]);
    }
  }

  return (
    <UserFrame>
      <Content title="Payment Settings" maxWidth="md">
        <BreadcrumbsHeader backHref={returnUri}>
          <MuiLink
            color="inherit"
            href={`/p/${profile?.pid}/root/marketplaces`}
          >
            Marketplaces
          </MuiLink>
          <MuiLink color="inherit" href={returnUri}>
            {marketplace?.name}
          </MuiLink>
          <Typography color="textPrimary">Payment Settings</Typography>
        </BreadcrumbsHeader>

        {!loading && (!profile || !marketplace) ? (
          <Alert severity="error" variant="filled">
            Invalid marketplace
          </Alert>
        ) : (
          <div>
            {error.length > 0 &&
              error.map((err) => (
                <Box key={err} mb={2}>
                  <Alert severity="error" variant="outlined">
                    {err}
                  </Alert>
                </Box>
              ))}
            <Card className={layoutClasses.card} variant="outlined">
              <div className={layoutClasses.progress}>
                {submitting && <LinearProgress />}
              </div>
              <CardHeader title="Payment Settings" />
              <CardContent>
                {!loading && (
                  <Formik
                    initialValues={initialValues}
                    validationSchema={PaymentSettingsSchema}
                    onSubmit={handleSubmit}
                  >
                    {({ dirty }) => (
                      <Form>
                        <PaymentSettingsFormFields />
                        <Box mt={4} display="flex" justifyContent="flex-end">
                          <Box>
                            <Link href={returnUri} passHref>
                              <Button
                                component="a"
                                variant="text"
                                color="primary"
                                size="large"
                              >
                                Cancel
                              </Button>
                            </Link>
                          </Box>
                          <Box ml={1}>
                            <Button
                              variant="contained"
                              color="primary"
                              size="large"
                              disabled={!dirty || submitting}
                              type="submit"
                            >
                              Save
                            </Button>
                          </Box>
                        </Box>
                      </Form>
                    )}
                  </Formik>
                )}
              </CardContent>
            </Card>
          </div>
        )}
      </Content>
    </UserFrame>
  );
}
