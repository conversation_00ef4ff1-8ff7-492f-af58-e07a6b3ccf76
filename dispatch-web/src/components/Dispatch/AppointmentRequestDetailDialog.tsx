import AppointmentCheckoutContent from '@/components/Checkout/CheckoutContent';
import ConfirmDialog from '@/components/ConfirmDialog';
import AppointmentRequestDialogTabs from '@/components/Dispatch/AppointmentRequestDialogTabs';
import AppointmentRequestInfoView from '@/components/Dispatch/AppointmentRequestInfoView';
import ConstraintDetail from '@/components/Dispatch/ConstraintDetail';
import CreateAppointmentRequestDialog from '@/components/Dispatch/CreateAppointmentRequestDialog';
import RequestStatus from '@/components/Dispatch/RequestStatus';
import MarketplaceUserProfileContent from '@/components/MarketplaceUser/MarketplaceUserProfileContent';
import StandardDialogTitle from '@/components/StandardDialogTitle';
import {
  AppointmentCandidateFieldsFragment,
  AppointmentConstraintFieldsFragment,
  AppointmentRequestStatus,
  AppointmentStatus,
  FullAppointmentFieldsFragment,
  FullAppointmentRequestFieldsFragment,
  RoleScope,
  useApproveAppointmentRequestMutation,
  useArchiveAppointmentRequestMutation,
  useCancelAppointmentRequestMutation,
  useClientProfileMarketplaceUserQuery,
  useMarketplaceDispatchQuery,
  useViewerQuery,
} from '@/generated/graphql';
import { useAuthorize } from '@/hooks/authorize';
import { useProfile } from '@/hooks/profile';
import { usePathHash } from '@/hooks/router';
import { formatGraphQLErrors } from '@/utils/apollo-errors';
import dayjs from '@/utils/dayjs';
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  Menu,
  MenuItem,
  makeStyles,
  useMediaQuery,
} from '@material-ui/core';
import * as Colors from '@material-ui/core/colors';
import { useTheme } from '@material-ui/core/styles';
import { CSSProperties } from '@material-ui/core/styles/withStyles';
import { Alert } from '@material-ui/lab';
import { find, flatMap } from 'lodash';
import { useSnackbar } from 'notistack';
import React, { useState } from 'react';

const editPermission = [
  'marketplaces:full',
  {
    scope: RoleScope.Marketplace,
    permission: ['marketplace.appointments:full'],
  },
];

interface AppointmentRequestDetailDialogProps {
  open: boolean;
  onClose: () => void;
  path: string;
  appointmentRequest?:
    | (Omit<FullAppointmentRequestFieldsFragment, 'constraints'> & {
        constraints: (AppointmentConstraintFieldsFragment & {
          candidates?: AppointmentCandidateFieldsFragment[];
          appointments?: FullAppointmentFieldsFragment[];
        })[];
      })
    | null;
}

const useStyles = makeStyles((theme) => ({
  sectionTitle: {
    ...theme.typography.body1,
    fontWeight: theme.typography.fontWeightBold as CSSProperties['fontWeight'],
    marginBottom: theme.spacing(0.5),
  },
  chip: {
    backgroundColor: Colors.blueGrey[100],
    color: theme.palette.getContrastText(Colors.blueGrey[100]),
    fontWeight: theme.typography.fontWeightBold as CSSProperties['fontWeight'],
  },
  appointment: {
    padding: theme.spacing(2),
    margin: theme.spacing(1, 0),
    border: `2px solid ${theme.palette.primary.main}`,
    borderRadius: theme.shape.borderRadius,
  },
}));

export default function AppointmentRequestDetailDialog({
  open,
  onClose,
  appointmentRequest = null,
  path,
}: AppointmentRequestDetailDialogProps): JSX.Element {
  const classes = useStyles();
  const theme = useTheme();
  const hash = usePathHash();
  const fullScreen = useMediaQuery(theme.breakpoints.down('xs'));
  const { enqueueSnackbar } = useSnackbar();

  const { profile } = useProfile();

  const [cancelErrors, setCancelErrors] = useState<string[]>([]);
  const [approveErrors, setApproveErrors] = useState<string[]>([]);
  const [archiveErrors, setArchiveErrors] = useState<string[]>([]);
  const [editing, setEditing] = useState(false);
  const [menuAnchor, setMenuAnchor] = useState<HTMLElement | null>(null);
  const [confirmCancelOpen, setConfirmCancelOpen] = useState<boolean>(false);
  const [confirmApproveOpen, setConfirmApproveOpen] = useState<boolean>(false);
  const [confirmArchiveOpen, setConfirmArchiveOpen] = useState<boolean>(false);
  const [selectedConstraintIndex, setSelectedConstraintIndex] = useState(-1);

  const [canEditPerm] = useAuthorize(editPermission, {
    resourceId: appointmentRequest?.marketplace.id,
  });

  const clientProfile = appointmentRequest?.clientProfiles?.[0];
  const tzid = clientProfile?.tzid ?? dayjs.tz.guess();

  const selectedConstraint =
    appointmentRequest?.constraints[selectedConstraintIndex];

  const marketplaceId = appointmentRequest?.marketplace.id as string;

  const { data: viewerData } = useViewerQuery();
  const viewer = viewerData?.viewer;

  const { data: marketplaceData } = useMarketplaceDispatchQuery({
    variables: { id: marketplaceId },
    skip: !appointmentRequest?.marketplace.id,
  });

  const marketplace = marketplaceData?.marketplace;

  const { data: marketplaceUserData } = useClientProfileMarketplaceUserQuery({
    variables: {
      id: clientProfile?.id as string,
      groupId: marketplace?.groupId as string,
    },
    skip: !clientProfile?.id || !marketplace?.groupId,
  });

  const marketplaceUser =
    marketplaceUserData?.clientProfile?.marketplaceUsers?.[0];

  const [cancelAppoinmentRequest, { loading: cancelLoading }] =
    useCancelAppointmentRequestMutation();

  const [approveAppoinmentRequest, { loading: approveLoading }] =
    useApproveAppointmentRequestMutation();

  const [archiveAppoinmentRequest, { loading: archiveLoading }] =
    useArchiveAppointmentRequestMutation();

  const submitting = cancelLoading || approveLoading || archiveLoading;

  const canApprove =
    !!appointmentRequest &&
    canEditPerm &&
    appointmentRequest.status === AppointmentRequestStatus.PendingApproval;

  const canCancel =
    !!appointmentRequest &&
    canEditPerm &&
    [
      AppointmentRequestStatus.Pending,
      AppointmentRequestStatus.PendingApproval,
    ].includes(appointmentRequest.status);

  const canEdit =
    !!appointmentRequest &&
    canEditPerm &&
    [AppointmentRequestStatus.PendingApproval].includes(
      appointmentRequest.status,
    );

  const canArchive =
    !!appointmentRequest &&
    !appointmentRequest.archivedAt &&
    canEditPerm &&
    appointmentRequest.status === AppointmentRequestStatus.Cancelled;

  const handleClose = () => {
    setEditing(false);
    setCancelErrors([]);
    setApproveErrors([]);
    setArchiveErrors([]);
    onClose();
  };

  const handleCancelAppointmentRequest = async () => {
    if (!appointmentRequest || !canCancel || submitting) {
      return;
    }

    try {
      await cancelAppoinmentRequest({
        variables: {
          input: { appointmentRequestId: appointmentRequest.id },
        },
      });

      enqueueSnackbar('Appointment request cancelled', {
        variant: 'success',
      });

      setConfirmCancelOpen(false);
      setMenuAnchor(null);
      setCancelErrors([]);
    } catch (err) {
      setCancelErrors(formatGraphQLErrors(err.graphQLErrors) ?? [err.message]);
    }
  };

  const handleApproveAppointmentRequest = async () => {
    if (!appointmentRequest || !canApprove || submitting) {
      return;
    }

    try {
      await approveAppoinmentRequest({
        variables: {
          input: { appointmentRequestId: appointmentRequest.id },
        },
      });

      enqueueSnackbar('Appointment request approved', {
        variant: 'success',
      });

      setConfirmApproveOpen(false);
      setMenuAnchor(null);
      setApproveErrors([]);
    } catch (err) {
      setApproveErrors(formatGraphQLErrors(err.graphQLErrors) ?? [err.message]);
    }
  };

  const handleArchiveAppointmentRequest = async () => {
    if (!appointmentRequest || !canArchive || submitting) {
      return;
    }

    try {
      await archiveAppoinmentRequest({
        variables: {
          input: { appointmentRequestId: appointmentRequest.id },
        },
      });

      enqueueSnackbar('Appointment request archived', {
        variant: 'info',
      });

      setConfirmArchiveOpen(false);
      setMenuAnchor(null);
      setArchiveErrors([]);
      handleClose();
    } catch (err) {
      setArchiveErrors(formatGraphQLErrors(err.graphQLErrors) ?? [err.message]);
    }
  };

  const handleCloseConstraintDetail = () => {
    setSelectedConstraintIndex(-1);
  };

  const appointment = find(
    flatMap(appointmentRequest?.constraints ?? [], (c) => c.appointments ?? []),
    (a) => a.status !== AppointmentStatus.Pending,
  );

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      fullScreen={fullScreen}
      maxWidth="sm"
      fullWidth
    >
      <StandardDialogTitle
        fullScreen={fullScreen}
        onClose={handleClose}
        {...(canEditPerm && {
          ...(canEdit &&
            appointmentRequest?.status ===
              AppointmentRequestStatus.PendingApproval && {
              onClickEdit: () => setEditing(true),
            }),
          onClickContext: (event: React.MouseEvent<HTMLElement>) =>
            setMenuAnchor(event.currentTarget),
        })}
      >
        <Box mb={-3}>
          <div>Appointment Request Details</div>
          {appointmentRequest?.status && (
            <RequestStatus variant={appointmentRequest.status} />
          )}
          <AppointmentRequestDialogTabs
            selected={hash.view ?? 'info'}
            path={path}
          />
        </Box>
      </StandardDialogTitle>

      {!hash.view && (
        <AppointmentRequestInfoView
          appointmentRequest={appointmentRequest}
          canEditPerm={canEditPerm}
          classes={classes}
          setSelectedConstraintIndex={setSelectedConstraintIndex}
        />
      )}

      {hash.view === 'account' && (
        <DialogContent dividers>
          {marketplaceUser ? (
            <MarketplaceUserProfileContent
              marketplaceUser={marketplaceUser}
              viewerProfiles={viewer?.profiles}
              popoutPath={`/p/${profile?.pid}/marketplace/accounts/${marketplaceUser.id}`}
            />
          ) : (
            <Alert severity="warning">
              A marketplace account is not linked to this request
            </Alert>
          )}
        </DialogContent>
      )}

      {hash.view === 'checkout' &&
        !!appointmentRequest &&
        (appointmentRequest.checkout ? (
          <AppointmentCheckoutContent
            checkout={appointmentRequest?.checkout}
            pending={!appointment?.id}
            fullScreen={fullScreen}
          />
        ) : (
          <DialogContent dividers>
            <Alert severity="warning">
              Payments are not enabled for this request
            </Alert>
          </DialogContent>
        ))}

      {(canCancel || canApprove) && !hash.view && (
        <DialogActions>
          <Box width="100%" display="flex" justifyContent="space-between">
            {canCancel && (
              <Button
                variant="text"
                color="secondary"
                type="submit"
                onClick={() => {
                  setConfirmCancelOpen(true);
                  setMenuAnchor(null);
                }}
                disabled={submitting}
              >
                Cancel request
              </Button>
            )}

            {canApprove && (
              <Button
                variant="contained"
                color="primary"
                type="submit"
                onClick={() => {
                  setConfirmApproveOpen(true);
                  setMenuAnchor(null);
                }}
                disabled={submitting}
              >
                Approve
              </Button>
            )}
          </Box>
        </DialogActions>
      )}

      <Menu
        open={Boolean(menuAnchor)}
        onClose={() => {
          setMenuAnchor(null);
        }}
        anchorOrigin={{ vertical: 'top', horizontal: 'left' }}
        transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        getContentAnchorEl={null}
        anchorEl={menuAnchor}
        keepMounted
      >
        {appointmentRequest?.status !== AppointmentRequestStatus.Cancelled ? (
          <MenuItem
            disabled={!canCancel || submitting}
            onClick={() => {
              setConfirmCancelOpen(true);
            }}
          >
            Cancel request
          </MenuItem>
        ) : (
          <MenuItem
            disabled={!canArchive || submitting}
            onClick={() => {
              setConfirmArchiveOpen(true);
            }}
          >
            Archive request
          </MenuItem>
        )}
      </Menu>

      {editing && marketplace && (
        <CreateAppointmentRequestDialog
          marketplaces={[marketplace]}
          title="Edit Appointment Request"
          open={editing}
          onClose={() => setEditing(false)}
          editRequest={appointmentRequest}
        />
      )}

      {!!selectedConstraint && !!appointmentRequest && !!profile && (
        <ConstraintDetail
          constraint={selectedConstraint}
          appointmentRequest={appointmentRequest}
          open={Boolean(selectedConstraint)}
          onClose={handleCloseConstraintDetail}
          profileId={profile.id}
          tzid={tzid}
        />
      )}

      <ConfirmDialog
        submitting={cancelLoading}
        title="Cancel appointment request?"
        description="Pending appointments linked to this request will alse be cancelled."
        cancelText="Nevermind"
        open={confirmCancelOpen}
        onConfirm={handleCancelAppointmentRequest}
        onCancel={() => {
          setConfirmCancelOpen(false);
          setMenuAnchor(null);
          setCancelErrors([]);
        }}
        errors={cancelErrors}
      />

      <ConfirmDialog
        submitting={approveLoading}
        title="Approve this request?"
        open={confirmApproveOpen}
        onConfirm={handleApproveAppointmentRequest}
        onCancel={() => {
          setConfirmApproveOpen(false);
          setMenuAnchor(null);
          setApproveErrors([]);
        }}
        errors={approveErrors}
      />

      <ConfirmDialog
        submitting={archiveLoading}
        title="Archive appointment request?"
        cancelText="Nevermind"
        open={confirmArchiveOpen}
        onConfirm={handleArchiveAppointmentRequest}
        onCancel={() => {
          setConfirmArchiveOpen(false);
          setMenuAnchor(null);
          setArchiveErrors([]);
        }}
        errors={archiveErrors}
        ConfirmButtonProps={{
          color: 'secondary',
        }}
      />
    </Dialog>
  );
}
