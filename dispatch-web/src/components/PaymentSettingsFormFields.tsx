import {
  Box,
  FormControl,
  FormControlLabel,
  FormHelperText,
  FormLabel,
  InputAdornment,
  InputLabel,
  MenuItem,
  Radio,
  RadioGroup,
} from '@material-ui/core';
import { Field, useFormikContext, FieldProps } from 'formik';
import { CheckboxWithLabel, TextField, Select } from 'formik-material-ui';
import * as yup from 'yup';

export const PaymentSettingsSchema = yup
  .object()
  .shape({
    paymentCollectionMethod: yup
      .string()
      .oneOf(['collect_on_confirmation', 'collect_on_site', 'collect_deposit'])
      .required('Required'),
    paymentDepositType: yup.string().when('paymentCollectionMethod', {
      is: 'collect_deposit',
      then: yup
        .string()
        .oneOf(['fixed_amount', 'percentage'])
        .required('Required when collecting deposit'),
      otherwise: yup.string().nullable(),
    }),
    paymentDepositValue: yup.number().when('paymentCollectionMethod', {
      is: 'collect_deposit',
      then: yup
        .number()
        .min(0, 'Must be greater than or equal to 0')
        .required('Required when collecting deposit'),
      otherwise: yup.number().nullable(),
    }),
    hasPaymentPolicy: yup.boolean().required('Required'),
    paymentPolicyName: yup.string().when('hasPaymentPolicy', {
      is: true,
      then: yup.string().required('Required when policy is enabled'),
      otherwise: yup.string().nullable(),
    }),
    paymentPolicyText: yup.string().when('hasPaymentPolicy', {
      is: true,
      then: yup.string().required('Required when policy is enabled'),
      otherwise: yup.string().nullable(),
    }),
    requirePaymentPolicyAttestation: yup.boolean().required('Required'),
  })
  .required();

export interface PaymentSettingsFields {
  paymentCollectionMethod: string;
  paymentDepositType: string;
  paymentDepositValue: number;
  hasPaymentPolicy: boolean;
  paymentPolicyName: string;
  paymentPolicyText: string;
  requirePaymentPolicyAttestation: boolean;
}

export default function PaymentSettingsFormFields(): JSX.Element {
  const { values, setFieldValue } = useFormikContext<PaymentSettingsFields>();

  return (
    <>
      <Box mb={1}>
        <FormControl component="fieldset" variant="outlined" fullWidth>
          <FormLabel component="legend">Payment Collection Method *</FormLabel>
          <Field name="paymentCollectionMethod">
            {({ field }: FieldProps) => (
              <RadioGroup
                {...field}
                value={field.value}
                style={{ marginTop: 16 }}
                onChange={(e) => {
                  setFieldValue('paymentCollectionMethod', e.target.value);
                  // Reset deposit fields when changing from collect_deposit
                  if (e.target.value !== 'collect_deposit') {
                    setFieldValue('paymentDepositType', 'fixed_amount');
                    setFieldValue('paymentDepositValue', 0);
                  }
                }}
              >
                <FormControlLabel
                  value="collect_on_confirmation"
                  control={<Radio color="primary" />}
                  label={
                    <Box mb={1}>
                      <Box>Collect payment once appointment is confirmed</Box>
                      <Box color="text.secondary" fontSize="0.875rem">
                        The user&apos;s account will be charged for the full
                        cost of services once the appointment is confirmed.
                      </Box>
                    </Box>
                  }
                />
                <FormControlLabel
                  value="collect_on_site"
                  control={<Radio color="primary" />}
                  label={
                    <Box mb={1}>
                      <Box>Collect payment on-site</Box>
                      <Box color="text.secondary" fontSize="0.875rem">
                        The user&apos;s account will not be charged in advance.
                        Their card will need to be charged at the time of the
                        appointment.
                      </Box>
                    </Box>
                  }
                />
                <FormControlLabel
                  value="collect_deposit"
                  control={<Radio color="primary" />}
                  label={
                    <Box mb={1}>
                      <Box>Collect a deposit once appointment is confirmed</Box>
                      <Box color="text.secondary" fontSize="0.875rem">
                        The user&apos;s account will be charged a deposit when
                        the appointment is confirmed. The remaining balance must
                        be collected at the time of the appointment.
                      </Box>
                    </Box>
                  }
                />
              </RadioGroup>
            )}
          </Field>
        </FormControl>
      </Box>

      {values.paymentCollectionMethod === 'collect_deposit' && (
        <>
          <Box mb={2}>
            <FormControl variant="outlined" fullWidth>
              <InputLabel htmlFor="payment-deposit-type-field">
                Deposit Type *
              </InputLabel>
              <Field
                component={Select}
                name="paymentDepositType"
                inputProps={{
                  id: 'payment-deposit-type-field',
                }}
                label="Deposit Type *"
              >
                <MenuItem value="fixed_amount">Fixed Amount</MenuItem>
                <MenuItem value="percentage">Percentage of Total</MenuItem>
              </Field>
            </FormControl>
          </Box>

          <Box mb={2}>
            <Field
              component={TextField}
              name="paymentDepositValue"
              label={`Deposit Value * ${
                values.paymentDepositType === 'fixed_amount' ? '($)' : '(%)'
              }`}
              type="number"
              variant="outlined"
              fullWidth
              inputProps={{
                min: 0,
                step:
                  values.paymentDepositType === 'fixed_amount' ? '0.01' : '1',
                max:
                  values.paymentDepositType === 'percentage' ? 100 : undefined,
              }}
              InputProps={{
                startAdornment:
                  values.paymentDepositType === 'fixed_amount' ? (
                    <InputAdornment position="start">$</InputAdornment>
                  ) : undefined,
                endAdornment:
                  values.paymentDepositType === 'percentage' ? (
                    <InputAdornment position="end">%</InputAdornment>
                  ) : undefined,
              }}
            />
            {values.paymentDepositType === 'fixed_amount' && (
              <FormHelperText>
                If total service cost ≤ deposit amount, do not collect deposit
                (treat as &quot;Collect payment once appointment is
                confirmed&quot;)
              </FormHelperText>
            )}
          </Box>
        </>
      )}

      <Box mb={2}>
        <FormControl variant="outlined" fullWidth>
          <Field
            component={CheckboxWithLabel}
            type="checkbox"
            color="primary"
            name="hasPaymentPolicy"
            Label={{ label: 'Add a Payment Policy' }}
          />
          <FormHelperText>
            Enable to add a custom payment policy that users will see during
            checkout.
          </FormHelperText>
        </FormControl>
      </Box>

      {values.hasPaymentPolicy && (
        <>
          <Box mb={2}>
            <Field
              component={TextField}
              name="paymentPolicyName"
              label="Policy Name *"
              placeholder="e.g., Cancellation Policy"
              variant="outlined"
              fullWidth
              inputProps={{ maxLength: 255 }}
            />
            <FormHelperText>
              Will display as header on confirmation page
            </FormHelperText>
          </Box>

          <Box mb={2}>
            <Field
              component={TextField}
              name="paymentPolicyText"
              label="Policy Text *"
              placeholder="Enter your payment policy content..."
              variant="outlined"
              multiline
              minRows={4}
              fullWidth
            />
            <FormHelperText>
              Multi-line textbox for free-form policy content
            </FormHelperText>
          </Box>

          <Box mb={2}>
            <FormControl variant="outlined" fullWidth>
              <Field
                component={CheckboxWithLabel}
                type="checkbox"
                color="primary"
                name="requirePaymentPolicyAttestation"
                Label={{ label: 'Require User Attestation to Policy' }}
              />
              <FormHelperText>
                If enabled, users will see a checkbox &quot;I agree to the
                [Policy Name]&quot; on the confirmation page.
              </FormHelperText>
            </FormControl>
          </Box>
        </>
      )}
    </>
  );
}
