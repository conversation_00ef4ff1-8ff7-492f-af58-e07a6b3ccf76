import {
  ProcedureBaseDefGroupFieldsFragment,
  ProcedureBaseDefFieldsFragment,
} from '@/generated/graphql';
import React from 'react';
import {
  Dialog,
  Checkbox,
  Button,
  DialogActions,
  DialogTitle,
  DialogContent,
  FormControlLabel,
  FormGroup,
} from '@material-ui/core';

export type Item =
  | Partial<ProcedureBaseDefFieldsFragment>
  | Partial<ProcedureBaseDefGroupFieldsFragment>;

type Props = {
  items: Item[];
  selectedItems: Item[] | null;
  setSelectedItems: (value: Item[]) => void;
  open: boolean;
  fullScreen: boolean;
  setOpen: (b: boolean) => void;
};

export default function SelectItemDialog({
  items,
  open,
  fullScreen,
  selectedItems,
  setSelectedItems,
  setOpen,
}: Props): JSX.Element {
  const [selected, setSelected] = React.useState<string[]>([]);

  const handleClick = (id: string) => {
    if (selected.includes(id)) setSelected(selected.filter((i) => i !== id));
    else setSelected([...selected, id]);
  };

  const handleCancel = () => {
    setOpen(false);
    setSelected([]);
  };

  const handleSave = () => {
    const newItems = items.filter((i: Item) =>
      selected.includes(i?.id as string),
    );
    setSelectedItems(
      selectedItems ? [...selectedItems, ...newItems] : newItems,
    );
    handleCancel();
  };

  return (
    <Dialog
      open={open}
      fullScreen={fullScreen}
      onClose={handleCancel}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle>Add Items</DialogTitle>
      <DialogContent dividers>
        <FormGroup>
          {items.map((i) => (
            <FormControlLabel
              key={i.id}
              control={
                <Checkbox
                  checked={selected.includes(i?.id as string)}
                  onClick={() => handleClick(i?.id as string)}
                />
              }
              label={i.name}
            />
          ))}
        </FormGroup>
        {!items.length && 'No items to select from.'}
      </DialogContent>
      <DialogActions>
        <Button onClick={handleCancel}>Cancel</Button>
        <Button onClick={handleSave} color="primary" variant="outlined">
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
}
