import CheckoutItem from '@/components/Checkout/CheckoutItem';
import CheckoutItemReadOnly, {
  currencyFormatter,
} from '@/components/Checkout/CheckoutItemReadOnly';
import SelectPaymentMethodDialog from '@/components/Checkout/SelectPaymentMethodDialog';
import SendReceiptDialog from '@/components/SendReceiptDialog';
import ConfirmDialog from '@/components/ConfirmDialog';
import {
  CheckoutItemFieldsFragment,
  CheckoutItemInput,
  CheckoutItemType,
  FullCheckoutFieldsFragment,
  PaymentFieldsFragment,
  PaymentStatus,
  PayoutStatus,
  useCompleteCheckoutMutation,
  useUpdateCheckoutMutation,
} from '@/generated/graphql';
import { useLayoutStyles } from '@/hooks/styles';
import { formatGraphQLErrors } from '@/utils/apollo-errors';
import { getCustomerContactFromCheckout } from '@/utils/checkout';
import dayjs from '@/utils/dayjs';
import {
  Box,
  Button,
  Collapse,
  DialogContent,
  Divider,
  IconButton,
  LinearProgress,
  Typography,
  makeStyles,
  useTheme,
} from '@material-ui/core';
import AddIcon from '@material-ui/icons/Add';
import ChevronIcon from '@material-ui/icons/ChevronRight';
import CardIcon from '@material-ui/icons/CreditCard';
import SaveIcon from '@material-ui/icons/Done';
import EditIcon from '@material-ui/icons/Edit';
import ErrorIcon from '@material-ui/icons/Error';
import { Alert } from '@material-ui/lab';
import clsx from 'clsx';
import { cloneDeep, isFinite, pick, sortBy, uniqueId } from 'lodash';
import { useSnackbar } from 'notistack';
import { useState, useMemo } from 'react';

const useStyles = makeStyles((theme) => ({
  dialogContent: {
    padding: 0,
  },
  container: {
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
  },
  status: {
    flex: '0',
    backgroundColor: theme.palette.background.default,
    padding: theme.spacing(2, 3),
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: theme.spacing(1),
  },
  content: {
    flex: '1',
    padding: theme.spacing(2, 3),
    overflowY: 'auto',
  },
  divider: {
    margin: theme.spacing(1, 0),
  },
  accordionIcon: {
    transform: 'rotate(0deg)',
    transition: 'transform 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms',
  },
  accordionExpanded: {
    transform: 'rotate(90deg)',
  },
  paymentRow: {
    ...theme.typography.body2,
    display: 'flex',
    flexDirection: 'row',
    gap: theme.spacing(1),
    marginBottom: theme.spacing(1),
    alignItems: 'center',
  },
  paymentDescription: {
    display: 'flex',
    alignItems: 'center',
  },
  paymentPrice: {
    flexGrow: 1,
    display: 'flex',
    justifyContent: 'flex-end',
    fontWeight: 500,
  },
  receiptButton: {
    flexGrow: 0,
    display: 'flex',
    justifyContent: 'flex-start',
    fontSize: theme.typography.body2.fontSize,
    lineHeight: theme.typography.body2.lineHeight,
    color: theme.palette.text.secondary,
    textTransform: 'none',
    textDecoration: 'underline',
    padding: 0,
    minWidth: 'auto',
    '&:hover': {
      backgroundColor: 'transparent',
      textDecoration: 'underline',
    },
  },
}));

interface CheckoutContentProps {
  checkout?: FullCheckoutFieldsFragment | null;
  pending?: boolean;
  fullScreen?: boolean;
  expandPayouts?: boolean;
}

export default function CheckoutContent({
  checkout,
  pending = false,
  fullScreen = false,
  expandPayouts = false,
}: CheckoutContentProps): JSX.Element {
  const classes = useStyles();
  const layoutClasses = useLayoutStyles();
  const theme = useTheme();

  const { enqueueSnackbar } = useSnackbar();

  const [error, setError] = useState<string[]>([]);
  const [editing, setEditing] = useState(false);
  const [editItems, setEditItems] = useState<CheckoutItemFieldsFragment[]>([]);
  const [validate, setValidate] = useState(false);
  const [addCardOpen, setAddCardOpen] = useState(false);
  const [confirmCompleteOpen, setConfirmCompleteOpen] = useState(false);
  const [showFailedPayments, setShowFailedPayments] = useState(false);
  const [showPayouts, setShowPayouts] = useState(expandPayouts);
  const [sendingReceipt, setSendingReceipt] = useState(false);
  const [selectedPayment, setSelectedPayment] =
    useState<PaymentFieldsFragment | null>(null);

  const [updateCheckout, { loading: loadingUpdate }] =
    useUpdateCheckoutMutation();

  const [completeCheckout, { loading: loadingComplete }] =
    useCompleteCheckoutMutation();

  const loading = loadingUpdate || loadingComplete;

  const customerContact = useMemo(
    () => getCustomerContactFromCheckout(checkout),
    [checkout],
  );

  const PaymentRow = ({ payment }: { payment: PaymentFieldsFragment }) => (
    <div className={classes.paymentRow}>
      <div className={classes.paymentDescription}>{payment.description}</div>
      <Button
        size="small"
        variant="text"
        className={classes.receiptButton}
        onClick={() => {
          setSelectedPayment(payment);
          setSendingReceipt(true);
        }}
      >
        Send receipt
      </Button>
      <div className={classes.paymentPrice}>
        {currencyFormatter.format(-payment.amount / 100)}
      </div>
    </div>
  );

  const orderedItems = sortBy(checkout?.items ?? [], (item) =>
    [
      CheckoutItemType.Gratuity,
      CheckoutItemType.Other,
      CheckoutItemType.Procedure,
    ].indexOf(item.type),
  ).reverse();

  const paymentInstrument = checkout?.paymentInstrument;

  const payments = checkout?.payments.filter(
    ({ status }) => status !== PaymentStatus.Failed,
  );

  const failedPayments = checkout?.payments.filter(
    ({ status }) => status === PaymentStatus.Failed,
  );

  const payouts = checkout?.payouts ?? [];

  const hasFailedPayouts = Boolean(
    checkout?.payouts.filter(({ status }) => status === PayoutStatus.Failed)
      .length,
  );

  const handleEdit = () => {
    setEditItems(cloneDeep(orderedItems));
    setEditing(true);
    setValidate(false);
  };

  const handleAddItem = () => {
    if (editing) {
      setEditItems((items) => [
        ...items,
        {
          id: uniqueId('new_'),
          type: CheckoutItemType.Other,
          description: '',
          price: Number.NaN,
          quantity: 1,
        },
      ]);
    }
  };

  const handleDeleteItem = (id: string) => {
    setEditItems((items) => items.filter((item) => item.id !== id));
  };

  const handleCancelEdit = () => {
    setEditItems([]);
    setEditing(false);
    setValidate(false);
    setError([]);
  };

  const handleUpdateItem = (updatedItem: CheckoutItemFieldsFragment) => {
    setEditItems((items) =>
      items.map((item) => (item.id === updatedItem.id ? updatedItem : item)),
    );
  };

  const handleUpdate = async () => {
    if (!checkout || !editing || loading) {
      return;
    }

    if (
      !editItems.every(
        ({ description, price }) =>
          description.trim().length > 0 && isFinite(price),
      )
    ) {
      setValidate(true);
      setError(['Please fill out all fields']);
      return;
    }

    const items = editItems
      .map<CheckoutItemInput>((item) =>
        item.type === CheckoutItemType.Other
          ? pick(item, ['id', 'price', 'type', 'quantity', 'description'])
          : pick(item, ['id', 'price']),
      )
      .map<CheckoutItemInput>(({ id, ...item }) => ({
        ...(id && isFinite(+id) && { id }),
        ...item,
      }));

    try {
      const { errors } = await updateCheckout({
        variables: {
          input: {
            id: checkout.id,
            items,
          },
        },
      });

      if (errors?.length) {
        setError(formatGraphQLErrors(errors) ?? ['Error updating checkout']);
      } else {
        enqueueSnackbar('Checkout updated', { variant: 'success' });
        handleCancelEdit();
      }
    } catch (err) {
      setError(formatGraphQLErrors(err.graphQLErrors) ?? [err.message]);
    }
  };

  const handleCompleteCheckout = async () => {
    if (!checkout || !paymentInstrument || pending || loading) {
      return;
    }
    setConfirmCompleteOpen(false);

    try {
      const { data, errors } = await completeCheckout({
        variables: {
          input: {
            checkoutId: checkout.id,
            expectedAmount: checkout.balance,
            isAdminPayment: true,
          },
        },
      });

      const result = data?.completeCheckout;

      if (errors?.length) {
        setError(formatGraphQLErrors(errors) ?? ['Error completing checkout']);
      } else if (!result?.success) {
        setError([result?.errorMessage ?? 'Error completing checkout']);
      } else {
        enqueueSnackbar('Checkout completed', { variant: 'success' });
        handleCancelEdit();
      }
    } catch (err) {
      setError(formatGraphQLErrors(err.graphQLErrors) ?? [err.message]);
    }
  };

  return (
    <DialogContent className={classes.dialogContent} dividers>
      <div className={layoutClasses.progress}>
        {loading && <LinearProgress />}
      </div>

      <div className={classes.container}>
        {editing && (
          <div className={classes.status}>
            <Button
              variant="contained"
              color="primary"
              startIcon={<SaveIcon />}
              onClick={handleUpdate}
              disabled={loading}
            >
              Save changes
            </Button>
            <Button
              variant="outlined"
              color="default"
              onClick={handleCancelEdit}
              disabled={loading}
            >
              Cancel
            </Button>
          </div>
        )}

        <div className={classes.content}>
          {error.length > 0 &&
            error.map((err) => (
              <Box key={err} mb={2}>
                <Alert severity="error" variant="standard">
                  {err}
                </Alert>
              </Box>
            ))}

          {editing ? (
            <>
              {editItems.map((item) => (
                <CheckoutItem
                  key={item.id}
                  item={item}
                  onUpdate={handleUpdateItem}
                  onDelete={handleDeleteItem}
                  validate={validate}
                  editing
                />
              ))}
              <Button
                variant="text"
                color="primary"
                onClick={handleAddItem}
                startIcon={<AddIcon />}
                disabled={loading}
              >
                Add item
              </Button>
            </>
          ) : (
            <>
              <Box
                display="flex"
                alignItems="center"
                style={{ gap: theme.spacing(1) }}
                mb={1}
              >
                <Typography variant="body1">
                  <strong>Checkout Details</strong>
                </Typography>

                <IconButton size="medium" onClick={handleEdit}>
                  <EditIcon />
                </IconButton>
              </Box>

              {orderedItems.map((item) => (
                <CheckoutItem key={item.id} item={item} />
              ))}

              {pending && (
                <Box mb={2}>
                  <Alert severity="info" variant="standard">
                    Procedures will be itemized after the appointment is
                    dispatched.
                  </Alert>
                </Box>
              )}

              {!!payments?.length && (
                <>
                  <Divider className={classes.divider} />

                  <CheckoutItemReadOnly
                    description="Subtotal"
                    price={checkout?.total}
                  />

                  {payments.map((p) => (
                    <PaymentRow key={p.id} payment={p} />
                  ))}
                </>
              )}

              <Divider className={classes.divider} />

              <CheckoutItemReadOnly
                description="Amount Due"
                price={checkout?.balance}
                variant="large"
              />

              {!!failedPayments?.length && (
                <>
                  <Box my={0}>
                    <Box display="flex" alignItems="center" style={{ gap: 8 }}>
                      <Typography variant="body1">
                        <strong>Failed Payments</strong>
                      </Typography>
                      <ErrorIcon style={{ color: theme.palette.error.main }} />
                      <IconButton
                        className={clsx(classes.accordionIcon, {
                          [classes.accordionExpanded]: showFailedPayments,
                        })}
                        size="medium"
                        onClick={() => setShowFailedPayments((on) => !on)}
                      >
                        <ChevronIcon />
                      </IconButton>
                    </Box>
                  </Box>

                  <Collapse in={showFailedPayments}>
                    {failedPayments.map((p) => (
                      <CheckoutItemReadOnly
                        key={p.id}
                        description={`${p.description} (${dayjs(
                          p.createdAt,
                        ).format('L LT')})`}
                        price={-p.amountRequested}
                      />
                    ))}
                  </Collapse>

                  <Divider className={classes.divider} />
                </>
              )}

              {payouts.length > 0 && (
                <>
                  <Box my={0}>
                    <Box display="flex" alignItems="center" style={{ gap: 8 }}>
                      <Typography variant="body1">
                        <strong>Payouts</strong>
                      </Typography>
                      {hasFailedPayouts && (
                        <ErrorIcon
                          style={{ color: theme.palette.error.main }}
                        />
                      )}
                      <IconButton
                        className={clsx(classes.accordionIcon, {
                          [classes.accordionExpanded]: showPayouts,
                        })}
                        size="medium"
                        onClick={() => setShowPayouts((on) => !on)}
                      >
                        <ChevronIcon />
                      </IconButton>
                    </Box>
                  </Box>

                  <Collapse in={showPayouts}>
                    {payouts.map((payout) => (
                      <CheckoutItemReadOnly
                        key={payout.id}
                        description={`${payout.description} (${
                          payout.status
                        } ${dayjs(payout.createdAt).format('L LT')})`}
                        price={payout.amountRequested}
                      />
                    ))}
                  </Collapse>

                  <Divider className={classes.divider} />
                </>
              )}

              <Box mb={1}>
                <Box display="flex" alignItems="center" style={{ gap: 8 }}>
                  <Typography variant="body1">
                    <strong>Payment Method</strong>
                  </Typography>

                  {Boolean(paymentInstrument) && (
                    <IconButton
                      size="medium"
                      onClick={() => setAddCardOpen(true)}
                    >
                      <EditIcon />
                    </IconButton>
                  )}
                </Box>

                {paymentInstrument ? (
                  <Box display="flex" alignItems="center">
                    <Box flexGrow={1} display="flex" alignItems="center">
                      <Box mr={1}>
                        <strong>{paymentInstrument.brand}</strong> ending in{' '}
                        {paymentInstrument.lastFour}
                      </Box>
                    </Box>

                    <Box minWidth={135} textAlign="right">
                      <Button
                        variant="contained"
                        color="primary"
                        onClick={() => setConfirmCompleteOpen(true)}
                        disabled={
                          loading || pending || Math.abs(checkout.balance) < 1
                        }
                      >
                        {checkout.balance >= 0
                          ? 'Pay balance'
                          : 'Refund balance'}
                      </Button>
                    </Box>
                  </Box>
                ) : (
                  <Box my={1}>
                    <Button
                      variant="text"
                      color="primary"
                      startIcon={<CardIcon />}
                      onClick={() => setAddCardOpen(true)}
                      disabled={loading}
                    >
                      Select card
                    </Button>
                  </Box>
                )}
              </Box>
            </>
          )}
        </div>
      </div>

      {!!checkout && (
        <SelectPaymentMethodDialog
          key={`SelectPaymentMethodDialog-${addCardOpen}`}
          open={addCardOpen}
          onClose={() => setAddCardOpen(false)}
          checkout={checkout}
          fullScreen={fullScreen}
        />
      )}

      {!!checkout && (
        <ConfirmDialog
          open={confirmCompleteOpen}
          onClose={() => setConfirmCompleteOpen(false)}
          onConfirm={handleCompleteCheckout}
          onCancel={() => setConfirmCompleteOpen(false)}
          title={`Confirm ${checkout.balance >= 0 ? 'payment' : 'refund'}`}
          description={
            checkout.balance > 0 ? (
              <>
                Payment amount:{' '}
                <strong>
                  {currencyFormatter.format(checkout.balance / 100)}
                </strong>
              </>
            ) : (
              <>
                Refund amount:{' '}
                <strong>
                  {currencyFormatter.format(-checkout.balance / 100)}
                </strong>
              </>
            )
          }
        />
      )}

      {selectedPayment && (
        <SendReceiptDialog
          open={sendingReceipt}
          onClose={() => {
            setSendingReceipt(false);
            setSelectedPayment(null);
          }}
          checkout={checkout}
          fullScreen={fullScreen}
          customerContact={customerContact}
          selectedPayment={selectedPayment}
        />
      )}
    </DialogContent>
  );
}
